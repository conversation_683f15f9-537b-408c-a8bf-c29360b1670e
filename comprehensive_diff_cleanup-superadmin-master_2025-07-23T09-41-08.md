# Comprehensive Diff Report

**Generated on:** 7/23/2025, 3:11:08 PM  
**Branch:** cleanup/superadmin-master  
**Comparison:** changes compared to origin/master  

---

## Table of Contents

1. [Summary](#summary)
2. [Git Diff](#git-diff)
3. [Complete File Contents](#complete-file-contents)

---

## Summary

**Total files changed:** 15

**Changed files:**
```
api/src/brands/brands.controller.spec.ts
api/src/brands/brands.controller.ts
api/src/brands/brands.service.spec.ts
api/src/brands/brands.service.ts
api/src/clinics/clinic.service.ts
ui/app/brands/page.tsx
ui/app/components/Layout/BrandsLayout.tsx
ui/app/components/Layout/LayoutConfiguration.tsx
ui/app/organisms/brand/ClinincDetail.tsx
ui/app/services/brand.queries.ts
ui/app/services/brands.services.ts
ui/app/services/clinic.queries.ts
ui/app/services/clinic.service.ts
ui/app/services/url.service.ts
ui/app/signin/pin/page.tsx
```

---

## Git Diff

```diff
Error generating diff
```

---

## Complete File Contents


### 📁 `api/src/brands/brands.controller.spec.ts`

**Lines:** 140 | **Size:** 4146 bytes

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { BrandController } from './brands.controller';
import { BrandService } from './brands.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { HttpException } from '@nestjs/common';

describe('BrandController', () => {
	let controller: BrandController;
	let service: any;
	let logger: any;

	beforeEach(async () => {
		const mockService = {
			createBrand: jest.fn(),
			getAllBrands: jest.fn(),
			getBrandById: jest.fn(),
			getBrandBySlug: jest.fn()
		};

		const mockLogger = {
			log: jest.fn(),
			error: jest.fn()
		};

		const module: TestingModule = await Test.createTestingModule({
			controllers: [BrandController],
			providers: [
				{
					provide: BrandService,
					useValue: mockService
				},
				{
					provide: WinstonLogger,
					useValue: mockLogger
				}
			]
		}).compile();

		controller = module.get<BrandController>(BrandController);
		service = module.get<BrandService>(BrandService);
		logger = module.get<WinstonLogger>(WinstonLogger);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('create', () => {
		it('should create a brand successfully', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Test Brand' };
			const mockBrand = { id: '1', name: 'Test Brand' };

			service.createBrand.mockResolvedValue(mockBrand);

			const result = await controller.create(createBrandDto);

			expect(service.createBrand).toHaveBeenCalledWith(createBrandDto);
			expect(result).toEqual(mockBrand);
		});

		it('should handle service errors', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Test Brand' };
			service.createBrand.mockRejectedValue(new Error('Service error'));

			await expect(controller.create(createBrandDto)).rejects.toThrow('Service error');
		});
	});

	describe('findbyId', () => {
		it('should return a brand by id', async () => {
			const mockBrand = { id: '1', name: 'Test Brand' };
			service.getBrandById.mockResolvedValue(mockBrand);

			const result = await controller.findbyId('1');

			expect(service.getBrandById).toHaveBeenCalledWith('1');
			expect(result).toEqual(mockBrand);
		});

		it('should return null when brand not found', async () => {
			service.getBrandById.mockResolvedValue(null);

			const result = await controller.findbyId('nonexistent');

			expect(result).toBeNull();
		});
	});

	describe('findAll', () => {
		it('should return paginated brands with default parameters', async () => {
			const mockResult = { brands: [{ id: '1', name: 'Test Brand' }], total: 1 };
			service.getAllBrands.mockResolvedValue(mockResult);

			const result = await controller.findAll();

			expect(logger.log).toHaveBeenCalledWith('Fetching all brands', {
				page: 1,
				limit: 10,
				orderBy: 'DESC'
			});
			expect(service.getAllBrands).toHaveBeenCalledWith(1, 10, '', 'DESC');
			expect(result).toEqual(mockResult);
		});

		it('should handle service errors and throw HttpException', async () => {
			service.getAllBrands.mockRejectedValue(new Error('Service error'));

			await expect(controller.findAll()).rejects.toThrow(HttpException);
			expect(logger.error).toHaveBeenCalledWith('Error fetching brands', {
				error: expect.any(Error)
			});
		});
	});

	describe('findBySlug', () => {
		it('should return brand by slug', async () => {
			const mockBrand = { id: '1', name: 'Test Brand', slug: 'test-brand' };
			service.getBrandBySlug.mockResolvedValue(mockBrand);

			const result = await controller.findBySlug('test-brand');

			expect(service.getBrandBySlug).toHaveBeenCalledWith('test-brand');
			expect(result).toEqual(mockBrand);
		});

		it('should return null when brand not found', async () => {
			service.getBrandBySlug.mockResolvedValue(null);

			const result = await controller.findBySlug('nonexistent');

			expect(result).toBeNull();
		});
	});
});

```


### 📁 `api/src/brands/brands.controller.ts`

**Lines:** 122 | **Size:** 3402 bytes

```typescript
import {
	Controller,
	Post,
	UseGuards,
	UsePipes,
	ValidationPipe,
	Get,
	Query,
	Param,
	DefaultValuePipe,
	ParseIntPipe,
	HttpException,
	HttpStatus
} from '@nestjs/common';
import { BrandService } from './brands.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import {
	ApiBearerAuth,
	ApiBody,
	ApiOperation,
	ApiResponse,
	ApiTags
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';

@ApiTags('Brands')
// @ApiBearerAuth()
// @UseGuards(JwtAuthGuard, RolesGuard)
@Controller('brands')
export class BrandController {
	constructor(
		private readonly brandService: BrandService,
		private readonly logger: WinstonLogger
	) {}

	@Post()
	@ApiOperation({ summary: 'Creating a new Brand' })
	@ApiBody({ type: CreateBrandDto })
	@ApiResponse({
		status: 201,
		description: 'The Brand has been successfully created.',
		type: CreateBrandDto
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@Roles(Role.SUPER_ADMIN)
	@UsePipes(ValidationPipe)
	@TrackMethod('create-brands')
	async create(@Query() createBrandDto: CreateBrandDto) {
		return await this.brandService.createBrand(createBrandDto);
	}

	@Get(':id')
	@ApiOperation({ summary: 'Getting a brand by Id' })
	@ApiResponse({
		status: 200,
		description: 'Successfully retrieved Brand.',
		type: [CreateBrandDto]
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@Roles(Role.SUPER_ADMIN)
	@TrackMethod('findbyId-brands')
	async findbyId(@Param('id') id: string) {
		return this.brandService.getBrandById(id);
	}

	@Get()
	@ApiOperation({ summary: 'Getting all Brands' })
	@ApiResponse({
		status: 200,
		description: 'Successfully retrieved all Brands.',
		type: [CreateBrandDto]
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@Roles(Role.SUPER_ADMIN)
	@TrackMethod('findAll-brands')
	async findAll(
		@Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
		@Query('limit', new DefaultValuePipe(10), ParseIntPipe)
		limit: number = 10,
		@Query('orderBy', new DefaultValuePipe('DESC')) orderBy: string = 'DESC'
	) {
		try {
			this.logger.log('Fetching all brands', {
				page,
				limit,
				orderBy
			});

			return await this.brandService.getAllBrands(page, limit, '', orderBy);
		} catch (error) {
			this.logger.error('Error fetching brands', { error });

			throw new HttpException(
				'Error fetching all the brands',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get('slug/:slug')
	@ApiOperation({
		summary: 'Getting a brand by slug with client booking settings info'
	})
	@ApiResponse({
		status: 200,
		description:
			'Successfully retrieved Brand by slug with client booking settings status.',
		type: BrandWithSettingsDto
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('findBySlug-brands')
	async findBySlug(@Param('slug') slug: string) {
		return this.brandService.getBrandBySlug(slug);
	}
}

```


### 📁 `api/src/brands/brands.service.spec.ts`

**Lines:** 208 | **Size:** 6237 bytes

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { BrandService } from './brands.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import { ConflictException, InternalServerErrorException } from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';

describe('BrandService', () => {
	let service: BrandService;
	let mockRepository: any;
	let mockLogger: any;

	beforeEach(async () => {
		mockRepository = {
			findOne: jest.fn(),
			save: jest.fn(),
			find: jest.fn(),
			findAndCount: jest.fn(),
			create: jest.fn()
		};

		mockLogger = {
			log: jest.fn(),
			error: jest.fn()
		};

		const module: TestingModule = await Test.createTestingModule({
			providers: [
				BrandService,
				{
					provide: getRepositoryToken(Brand),
					useValue: mockRepository
				},
				{
					provide: WinstonLogger,
					useValue: mockLogger
				}
			]
		}).compile();

		service = module.get<BrandService>(BrandService);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('createBrand', () => {
		it('should create a new brand successfully', async () => {
			const createBrandDto: CreateBrandDto = { name: 'New Brand' };
			const mockBrand = { id: '1', name: 'New Brand', slug: 'newbrand' };

			mockRepository.findOne.mockResolvedValue(null);
			mockRepository.create.mockReturnValue(mockBrand);
			mockRepository.save.mockResolvedValue(mockBrand);

			const result = await service.createBrand(createBrandDto);

			expect(mockRepository.findOne).toHaveBeenCalledWith({
				where: { name: createBrandDto.name }
			});
			expect(mockRepository.create).toHaveBeenCalledWith(createBrandDto);
			expect(mockRepository.save).toHaveBeenCalledWith(mockBrand);
			expect(result).toEqual(mockBrand);
		});

		it('should throw ConflictException if brand already exists', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Existing Brand' };
			const existingBrand = { id: '1', name: 'Existing Brand' };

			mockRepository.findOne.mockResolvedValue(existingBrand);

			await expect(service.createBrand(createBrandDto)).rejects.toThrow(
				ConflictException
			);
			expect(mockRepository.create).not.toHaveBeenCalled();
			expect(mockRepository.save).not.toHaveBeenCalled();
		});

		it('should throw InternalServerErrorException on unexpected error', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Error Brand' };

			mockRepository.findOne.mockRejectedValue(new Error('Database error'));

			await expect(service.createBrand(createBrandDto)).rejects.toThrow(
				InternalServerErrorException
			);
		});
	});

	describe('getAllBrands', () => {
		it('should return paginated brands with default parameters', async () => {
			const mockBrands = [{ id: '1', name: 'Test Brand' }];
			mockRepository.findAndCount.mockResolvedValue([mockBrands, 1]);

			const result = await service.getAllBrands();

			expect(mockRepository.findAndCount).toHaveBeenCalledWith({
				where: {},
				skip: 0,
				take: 10,
				order: { createdAt: 'DESC' }
			});
			expect(result).toEqual({ brands: mockBrands, total: 1 });
		});

		it('should return paginated brands with search', async () => {
			const mockBrands = [{ id: '1', name: 'Test Brand' }];
			mockRepository.findAndCount.mockResolvedValue([mockBrands, 1]);

			const result = await service.getAllBrands(1, 10, 'test', 'ASC');

			expect(mockRepository.findAndCount).toHaveBeenCalledWith({
				where: { name: Like('%test%') },
				skip: 0,
				take: 10,
				order: { createdAt: 'ASC' }
			});
			expect(result).toEqual({ brands: mockBrands, total: 1 });
		});

		it('should throw InternalServerErrorException on error', async () => {
			mockRepository.findAndCount.mockRejectedValue(new Error('Database error'));

			await expect(service.getAllBrands()).rejects.toThrow(
				InternalServerErrorException
			);
		});
	});

	describe('getBrandById', () => {
		it('should return a brand by id', async () => {
			const mockBrand = { id: '1', name: 'Test Brand' };
			mockRepository.findOne.mockResolvedValue(mockBrand);

			const result = await service.getBrandById('1');

			expect(mockRepository.findOne).toHaveBeenCalledWith({
				where: { id: '1' }
			});
			expect(result).toEqual(mockBrand);
		});

		it('should return null if brand not found', async () => {
			mockRepository.findOne.mockResolvedValue(null);

			const result = await service.getBrandById('nonexistent');

			expect(result).toBeNull();
		});
	});

	describe('getAllBrandsSimple', () => {
		it('should return all brands without pagination', async () => {
			const mockBrands = [{ id: '1', name: 'Test Brand' }];
			mockRepository.find.mockResolvedValue(mockBrands);

			const result = await service.getAllBrandsSimple();

			expect(mockRepository.find).toHaveBeenCalledWith({
				order: { createdAt: 'DESC' }
			});
			expect(result).toEqual(mockBrands);
		});
	});

	describe('getBrandBySlug', () => {
		it('should return brand with settings', async () => {
			const mockBrandWithClinics = {
				id: '1',
				name: 'Test Brand',
				slug: 'testbrand',
				clinics: [{
					id: 'clinic-1',
					name: 'Test Clinic',
					phoneNumbers: [{ country_code: '+1', number: '1234567890' }],
					customRule: {
						clientBookingSettings: { isEnabled: true }
					}
				}]
			};
			mockRepository.findOne.mockResolvedValue(mockBrandWithClinics);

			const result = await service.getBrandBySlug('testbrand');

			expect(mockRepository.findOne).toHaveBeenCalledWith({
				where: { slug: 'testbrand' },
				relations: ['clinics']
			});
			expect(result?.hasClientBookingEnabled).toBe(true);
		});

		it('should return null when brand not found', async () => {
			mockRepository.findOne.mockResolvedValue(null);

			const result = await service.getBrandBySlug('nonexistent');

			expect(result).toBeNull();
		});
	});
});

```


### 📁 `api/src/brands/brands.service.ts`

**Lines:** 202 | **Size:** 5410 bytes

```typescript
import {
	BadRequestException,
	ConflictException,
	Injectable,
	InternalServerErrorException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';

@Injectable()
export class BrandService {
	constructor(
		@InjectRepository(Brand)
		private brandRepository: Repository<Brand>,
		private readonly logger: WinstonLogger
	) {}

	async createBrand(createBrandDto: CreateBrandDto): Promise<Brand> {
		try {
			this.logger.log('Creating Brand', { dto: createBrandDto });
			const existingBrand = await this.brandRepository.findOne({
				where: { name: createBrandDto.name }
			});
			if (existingBrand) {
				this.logger.error('Brand already exists', {
					email: createBrandDto.name
				});
				throw new ConflictException(
					'Brand with this name already exists'
				);
			}
			const brand = this.brandRepository.create(createBrandDto);
			return await this.brandRepository.save(brand);
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while creating the user'
			);
		}
	}

	async getAllBrands(
		page: number = 1,
		limit: number = 10,
		search: string = '',
		orderBy: string = 'DESC'
	): Promise<{ brands: Brand[]; total: number }> {
		try {
			this.logger.log('Fetching all Brands', {
				page,
				limit,
				search,
				orderBy
			});

			const whereCondition = search
				? { name: Like(`%${search}%`) }
				: {};

			const [brands, total] = await this.brandRepository.findAndCount({
				where: whereCondition,
				skip: (page - 1) * limit,
				take: limit,
				order:
					orderBy === 'ASC' ? { createdAt: 'ASC' } : { createdAt: 'DESC' }
			});

			this.logger.log('Fetched all Brands:', {
				brandsCount: brands.length,
				total,
				page,
				limit
			});

			return { brands, total };
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brands'
			);
		}
	}

	// Backward compatibility method for non-paginated calls
	async getAllBrandsSimple(): Promise<Brand[]> {
		try {
			this.logger.log('Fetching all Brands (simple)');
			const brands = await this.brandRepository.find({
				order: { createdAt: 'DESC' }
			});
			this.logger.log('Fetched all Brands (simple):', { brands });
			return brands;
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brands'
			);
		}
	}

	async getBrandById(id: string): Promise<Brand | null> {
		try {
			this.logger.log('Fetching a Brand');
			const brand = await this.brandRepository.findOne({ where: { id } });
			this.logger.log('Fetched a Brand:', { brand });
			return brand;
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brand'
			);
		}
	}

	async getBrandBySlug(slug: string): Promise<BrandWithSettingsDto | null> {
		try {
			this.logger.log('Fetching a Brand');
			const brand = await this.brandRepository.findOne({
				where: { slug },
				relations: ['clinics']
			});

			if (!brand) {
				return null;
			}

			const hasClientBookingEnabled =
				brand.clinics?.some(
					clinic =>
						clinic.customRule?.clientBookingSettings?.isEnabled ===
						true
				) || false;

			const brandDto = new BrandWithSettingsDto();

			// Copy only the basic brand properties (excluding clinics)
			brandDto.id = brand.id;
			brandDto.name = brand.name;
			brandDto.slug = brand.slug;
			brandDto.createdAt = brand.createdAt;
			brandDto.updatedAt = brand.updatedAt;
			brandDto.createdBy = brand.createdBy;
			brandDto.updatedBy = brand.updatedBy;

			// Add the calculated client booking flag
			brandDto.hasClientBookingEnabled = hasClientBookingEnabled;

			// Include clinics with their contact numbers
			if (brand.clinics && brand.clinics.length > 0) {
				// Include basic clinic info including phone numbers
				brandDto.clinics = brand.clinics.map(clinic => ({
					id: clinic.id,
					name: clinic.name,
					phoneNumbers: clinic.phoneNumbers || []
				}));
			}

			this.logger.log('Fetched a Brand with settings:', {
				brandId: brand.id,
				hasClientBookingEnabled,
				clinicsCount: brand.clinics?.length || 0
			});

			return brandDto;
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brand'
			);
		}
	}
}

```


### 📁 `api/src/clinics/clinic.service.ts`

**Lines:** 1485 | **Size:** 43402 bytes

```typescript
import {
	BadRequestException,
	ConflictException,
	Injectable,
	InternalServerErrorException,
	Inject,
	forwardRef
} from '@nestjs/common';
import { CreateClinicDto, UpdateBasicClinicDto } from './dto/create-clinic.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { ClinicEntity } from './entities/clinic.entity';
import { User } from '../users/entities/user.entity';
import { NotFoundException } from '@nestjs/common';
import { DataSource, In, Repository, IsNull } from 'typeorm';
import { ClinicRoomEntity } from './entities/clinic-room.entity';
import { UpdateClinicDto } from './dto/update-clinic.dto';
import { ReadService } from '../utils/excel/read.service';
import { FormatService } from '../utils/excel/format.service';
import { ClinicConsumblesService } from '../clinic-consumables/clinic-consumbles.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicMedicationsService } from '../clinic-medications/clinic-medications.service';
import { ClinicProductsService } from '../clinic-products/clinic-products.service';
import { ClinicServicesService } from '../clinic-services/clinic-services.service';
import { ClinicVaccinationsService } from '../clinic-vaccinations/clinic-vaccinations.service';
import { ClinicLabReportService } from '../clinic-lab-report/clinic-lab-report.service';
import * as XLSX from 'xlsx';
import * as bcrypt from 'bcrypt';
import {
	ConsumableHeaders,
	ProductHeaders,
	ServiceHeaders,
	DiagnosticHeaders,
	VaccinationHeaders,
	MedicationHeaders
} from './enum/inventory-headers.enum';
import { UsersService } from '../users/users.service';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { ClinicUser } from './entities/clinic-user.entity';
import {
	CreateClinicRoomDto,
	UpdateClinicRoomDto
} from './dto/create-clinic-room.dto';
import { ADMIN_ROLE_ID, DEV_SES_EMAIL } from '../utils/constants';
import { getLoginUrl, isProduction } from '../utils/common/get-login-url';
import { BrandService } from '../brands/brands.service';
import { UpdateClientBookingSettingsDto } from './dto/update-client-booking-settings.dto';
import { ClientBookingSettings } from './entities/clinic.entity';
import { ClientBookingSettingsResponseDto } from './dto/client-booking-settings-response.dto';
import {
	ClinicSettingsDto,
	ClinicSettingsResponseDto
} from './dto/clinic-settings.dto';

@Injectable()
export class ClinicService {
	constructor(
		@InjectRepository(ClinicEntity)
		private clinicRepository: Repository<ClinicEntity>,
		@InjectRepository(ClinicRoomEntity)
		private clinicRoomRepository: Repository<ClinicRoomEntity>,
		@InjectRepository(ClinicUser)
		private clinicUserRepository: Repository<ClinicUser>,
		private readonly logger: WinstonLogger,
		private readonly consumblesService: ClinicConsumblesService,
		private readonly clinicMedicationsService: ClinicMedicationsService,
		private readonly productsService: ClinicProductsService,
		private readonly clinicServices: ClinicServicesService,
		private readonly vaccinationService: ClinicVaccinationsService,
		private readonly clinicLabReportService: ClinicLabReportService,
		@Inject(forwardRef(() => UsersService))
		private userService: UsersService,
		private readonly mailService: SESMailService,
		private dataSource: DataSource,
		@Inject(forwardRef(() => BrandService))
		private brandService: BrandService
	) {}

	async createClinic(
		createClinicDto: CreateClinicDto,
		createdBy: string
	): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logger.log('Creating new clinic', { dto: createClinicDto });

			const {
				name,
				brandId,
				adminFirstName,
				adminLastName,
				adminEmail,
				adminMobile
			} = createClinicDto;
			console.log({
				name,
				brandId,
				adminFirstName,
				adminLastName,
				adminEmail,
				adminMobile
			});

			const existingClinic = await queryRunner.manager.findOne(
				ClinicEntity,
				{ where: { name } }
			);
			if (existingClinic) {
				throw new ConflictException(
					`The clinic with name ${name} already exists`
				);
			}

			const existingUser = await queryRunner.manager.findOne(User, {
				where: { email: adminEmail }
			});
			if (existingUser) {
				throw new ConflictException(
					`An Admin with email ${adminEmail} already exists`
				);
			}

			const pin = await this.userService.generateUniquePin();
			const hashedPin = await bcrypt.hash(pin, 10);
			const newUser = queryRunner.manager.create(User, {
				firstName: adminFirstName,
				lastName: adminLastName,
				email: adminEmail,
				roleId: ADMIN_ROLE_ID,
				mobileNumber: adminMobile,
				pin: hashedPin,
				createdBy,
				updatedBy: createdBy
			});

			await queryRunner.manager.save(newUser);

			const newClinic = queryRunner.manager.create(ClinicEntity, {
				name,
				brandId,
				adminFirstName,
				adminLastName,
				adminEmail,
				adminMobile,
				createdBy,
				updatedBy: createdBy
			});

			await queryRunner.manager.save(newClinic);

			const clinicId = newClinic.id;
			const userId = newUser.id;
			const newClinicUser = queryRunner.manager.create(ClinicUser, {
				clinicId,
				userId,
				isPrimary: true,
				brandId,
				createdBy,
				updatedBy: createdBy
			});

			await queryRunner.manager.save(newClinicUser);
			const brandInfo = await this.brandService.getBrandById(brandId);
			const loginUrl = getLoginUrl(brandInfo?.slug as string); //'https://nidanaqa-api.napses.in/sigin/pin';
			const subject = 'Clinic Registration';
			const body = `
				Dear ${adminFirstName} ${adminLastName},
				Your clinic has been registered with us. Your PIN is: ${pin}. Please use this PIN to log in to your account.
				The URL to login to your clinic is ${loginUrl}
			`;
			console.log(body);
			if (isProduction() && adminEmail) {
				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: adminEmail
				});
			} else if (!isProduction()) {
				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: DEV_SES_EMAIL //adminEmail
				});
			}

			await queryRunner.commitTransaction();
			return newClinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error creating new clinic', { error });

			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while creating/associating the user'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async getAllClinics(
		page: number = 1,
		limit: number = 10,
		orderBy: string = 'DESC'
	): Promise<{ clinics: ClinicEntity[]; total: number }> {
		const [clinics, total] = await this.clinicRepository.findAndCount({
			where: { deletedAt: IsNull() },
			relations: ['brand'],
			skip: (page - 1) * limit,
			take: limit,
			order:
				orderBy === 'ASC' ? { createdAt: 'ASC' } : { createdAt: 'DESC' }
		});

		return { clinics, total };
	}

	async updateBasicClinicInfo(
		id: string,
		updateBasicClinicDto: UpdateBasicClinicDto,
		userId: string
	): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();
		try {
			const clinic = await queryRunner.manager.findOne(ClinicEntity, {
				where: { id }
			});
			if (!clinic) {
				throw new NotFoundException(`Clinic with ID "${id}" not found`);
			}

			const clinicUser = await queryRunner.manager.findOne(ClinicUser, {
				where: { clinicId: clinic.id, isPrimary: true },
				relations: ['user']
			});
			if (!clinicUser) {
				throw new NotFoundException(
					`Primary admin for Clinic with ID "${id}" not found`
				);
			}

			const user = await queryRunner.manager.findOne(User, {
				where: { id: clinicUser.userId }
			});
			let isEmailChanged = false;

			if (user && user.email !== updateBasicClinicDto.adminEmail) {
				isEmailChanged = true;

				const existingUser = await queryRunner.manager.findOne(User, {
					where: { email: updateBasicClinicDto.adminEmail }
				});
				if (existingUser) {
					throw new ConflictException(
						`An admin with email ${updateBasicClinicDto.adminEmail} already exists`
					);
				}
			}

			if (user) {
				user.firstName = updateBasicClinicDto.adminFirstName;
				user.lastName = updateBasicClinicDto.adminLastName;
				user.mobileNumber = updateBasicClinicDto.adminMobile;
				user.updatedBy = userId;
			}

			if (user && isEmailChanged) {
				user.email = updateBasicClinicDto.adminEmail;
			}
			await queryRunner.manager.save(user);

			Object.assign(clinic, updateBasicClinicDto);
			await queryRunner.manager.save(clinic);
			if (isEmailChanged) {
				const subject = 'Your New Admin Credentials';
				const body = `
					Dear ${updateBasicClinicDto.adminFirstName} ${updateBasicClinicDto.adminLastName},
	
					Your new password is 8907. Please use the following link to log in.
				`;

				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: updateBasicClinicDto.adminEmail
				});
			}
			await queryRunner.commitTransaction();

			return clinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();

			this.logger.error('Error updating clinic', { error });

			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}

			throw new InternalServerErrorException(
				'An unexpected error occurred while updating the clinic and associated user'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async updateClinic(
		id: string,
		updateClinicDto: UpdateClinicDto,
		userId: string
	): Promise<ClinicEntity> {
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id }
			});
			if (!clinic) {
				throw new NotFoundException(`Clinic with ID "${id}" not found`);
			}

			// Handle phone numbers separately
			if (updateClinicDto.phoneNumbers) {
				clinic.phoneNumbers = updateClinicDto.phoneNumbers
					.filter(phone => phone.country_code && phone.number)
					.map(phone => ({
						country_code: phone.country_code!,
						number: phone.number!
					}));
			}

			// Handle customRule separately with validation
			if (updateClinicDto.customRule !== undefined) {
				if (
					typeof updateClinicDto.customRule
						.patientLastNameAsOwnerLastName !== 'boolean'
				) {
					throw new BadRequestException(
						'patientLastNameAsOwnerLastName must be a boolean value'
					);
				}
				clinic.customRule = {
					patientLastNameAsOwnerLastName:
						updateClinicDto.customRule
							.patientLastNameAsOwnerLastName
				};
			}

			const { ...restOfDto } = updateClinicDto;

			Object.assign(clinic, restOfDto);

			clinic.updatedBy = userId;

			const updatedClinic = await this.clinicRepository.save(clinic);

			// Ensure customRule is included in response
			return {
				...updatedClinic,
				customRule: updatedClinic.customRule || {
					patientLastNameAsOwnerLastName: false
				}
			};
		} catch (error) {
			this.logger.error('Error updating clinic', { error });

			if (error instanceof BadRequestException) {
				throw error;
			}

			throw new InternalServerErrorException(
				'An unexpected error occurred while updating the clinic'
			);
		}
	}

	async getClinicById(id: string): Promise<ClinicEntity> {
		const clinic = await this.clinicRepository.findOne({ where: { id } });
		if (!clinic) {
			throw new NotFoundException(`This clinic with ${id} doesn't exist`);
		}

		// Ensure customRule is included in response with default value if not set
		return {
			...clinic,
			customRule: clinic.customRule || {
				patientLastNameAsOwnerLastName: false
			}
		};
	}

	async getClinicRooms(id: string) {
		const clinic = await this.clinicRepository.findOne({ where: { id } });
		if (!clinic) {
			throw new NotFoundException(`This clinic with ${id} doesn't exist`);
		}

		const [rooms, total] = await this.clinicRoomRepository.findAndCount({
			where: { clinicId: id }
		});
		return { rooms, total };
	}

	async createClinicRoom(
		createClinicRoomDto: CreateClinicRoomDto,
		brandId: string
	): Promise<ClinicRoomEntity> {
		const clinic = await this.clinicRepository.findOne({
			where: { id: createClinicRoomDto.clinicId }
		});
		if (!clinic) {
			throw new NotFoundException(
				`Clinic with ID ${createClinicRoomDto.clinicId} not found`
			);
		}

		const newRoom = this.clinicRoomRepository.create({
			...createClinicRoomDto,
			brandId: brandId
		});
		return await this.clinicRoomRepository.save(newRoom);
	}

	async updateClinicRoom(
		id: string,
		updateClinicRoomDto: UpdateClinicRoomDto
	): Promise<ClinicRoomEntity> {
		const room = await this.clinicRoomRepository.findOne({ where: { id } });
		if (!room) {
			throw new NotFoundException(`Clinic room with ID ${id} not found`);
		}

		if (updateClinicRoomDto.clinicId) {
			const clinic = await this.clinicRepository.findOne({
				where: { id: updateClinicRoomDto.clinicId }
			});
			if (!clinic) {
				throw new NotFoundException(
					`Clinic with ID ${updateClinicRoomDto.clinicId} not found`
				);
			}
		}

		Object.assign(room, updateClinicRoomDto);
		return await this.clinicRoomRepository.save(room);
	}

	async deleteRoom(id: string): Promise<void> {
		const result = await this.clinicRoomRepository.delete(id);
		if (result.affected === 0) {
			throw new NotFoundException(`Room with ID "${id}" not found`);
		}
	}

	async deactivateClinic(id: string): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const clinic = await queryRunner.manager.findOne(ClinicEntity, {
				where: { id, deletedAt: IsNull() }
			});

			if (!clinic) {
				throw new NotFoundException(`Clinic with id ${id} not found`);
			}

			if (!clinic.isActive) {
				throw new ConflictException(
					`Clinic with id ${id} is already deactivated`
				);
			}

			clinic.isActive = false;
			await queryRunner.manager.save(clinic);

			const clinicUsers = await queryRunner.manager.find(ClinicUser, {
				where: { clinicId: id }
			});

			if (clinicUsers.length > 0) {
				const userIds = clinicUsers.map(
					clinicUser => clinicUser.userId
				);

				await queryRunner.manager.update(
					User,
					{ id: In(userIds) },
					{ isActive: false }
				);
			}

			await queryRunner.commitTransaction();
			return clinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error deactivating clinic', { error });

			if (
				error instanceof NotFoundException ||
				error instanceof ConflictException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An error occurred during clinic deactivation'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async reactivateClinic(id: string): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const clinic = await queryRunner.manager.findOne(ClinicEntity, {
				where: { id, deletedAt: IsNull() }
			});

			if (!clinic) {
				throw new NotFoundException(`Clinic with id ${id} not found`);
			}

			if (clinic.isActive) {
				throw new ConflictException(
					`Clinic with id ${id} is already active`
				);
			}

			clinic.isActive = true;
			await queryRunner.manager.save(clinic);

			const clinicUsers = await queryRunner.manager.find(ClinicUser, {
				where: { clinicId: id }
			});

			if (clinicUsers.length > 0) {
				const userIds = clinicUsers.map(
					clinicUser => clinicUser.userId
				);

				await queryRunner.manager.update(
					User,
					{ id: In(userIds) },
					{ isActive: true }
				);
			}

			await queryRunner.commitTransaction();
			return clinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error reactivating clinic', { error });

			if (
				error instanceof NotFoundException ||
				error instanceof ConflictException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An error occurred during clinic reactivation'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async softDeleteClinic(id: string): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const clinic = await queryRunner.manager.findOne(ClinicEntity, {
				where: { id, deletedAt: IsNull() }
			});

			if (!clinic) {
				throw new NotFoundException(`Clinic with id ${id} not found`);
			}

			if (clinic.deletedAt) {
				throw new ConflictException(
					`Clinic with id ${id} is already deleted`
				);
			}

			clinic.deletedAt = new Date();
			clinic.isActive = false; // Also deactivate when soft deleting
			await queryRunner.manager.save(clinic);

			// Also deactivate all users associated with this clinic
			const clinicUsers = await queryRunner.manager.find(ClinicUser, {
				where: { clinicId: id }
			});

			if (clinicUsers.length > 0) {
				const userIds = clinicUsers.map(
					clinicUser => clinicUser.userId
				);

				await queryRunner.manager.update(
					User,
					{ id: In(userIds) },
					{ isActive: false }
				);
			}

			await queryRunner.commitTransaction();
			return clinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error soft deleting clinic', { error });

			if (
				error instanceof NotFoundException ||
				error instanceof ConflictException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An error occurred during clinic soft deletion'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async processBulkUpload(
		file: Express.Multer.File,
		clinicId: string,
		brandId: string
	) {
		const sheetData = await ReadService.readExcelBuffer(file.buffer);
		const results: { [sheetName: string]: any } = {};

		for (const [sheetName, data] of Object.entries(sheetData)) {
			this.logger.log(`Processing sheet: ${sheetName}`);

			try {
				switch (sheetName.toLowerCase()) {
					case 'consumables':
						results[sheetName] = await this.processConsumables(
							data,
							clinicId,
							brandId
						);
						break;
					case 'medications':
						results[sheetName] = await this.processMedications(
							data,
							clinicId,
							brandId
						);
						break;
					case 'products':
						results[sheetName] = await this.processProducts(
							data,
							clinicId,
							brandId
						);
						break;
					case 'services':
						results[sheetName] = await this.processServices(
							data,
							clinicId,
							brandId
						);
						break;
					case 'vaccinations':
						results[sheetName] = await this.processVaccinations(
							data,
							clinicId,
							brandId
						);
						break;
					case 'diagnostics':
						results[sheetName] = await this.processDiagnostics(
							data,
							clinicId,
							brandId
						);
						break;
					default:
						this.logger.error(`Unknown sheet type: ${sheetName}`);
						continue;
				}
			} catch (error) {
				this.logger.error(`Error processing ${sheetName}`, error);
				results[sheetName] = {
					summary: { created: 0, updated: 0, failed: 0 },
					errors: [],
					errorsUpdate: [
						{
							id: 'bulk-operation',
							name: sheetName,
							errors: [`Failed to process sheet: ${sheetName}`]
						}
					]
				};
			}
		}

		return results;
	}

	private async processMedications(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicMedications(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(
			formattedData,
			this.clinicMedicationsService
		);
	}

	private async processConsumables(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicConsumables(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(formattedData, this.consumblesService);
	}

	private async processVaccinations(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicVaccinations(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(formattedData, this.vaccinationService);
	}

	private async processServices(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicServices(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(formattedData, this.clinicServices);
	}

	private async processProducts(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicProducts(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(formattedData, this.productsService);
	}

	private async processDiagnostics(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicLabReports(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(
			formattedData,
			this.clinicLabReportService
		);
	}

	private async processItems<T>(
		formattedData: { insertArray: T[]; errorArray: any[] },
		service: any
	) {
		const summary = { updated: 0, created: 0, failed: 0 };
		const errorArray = [];
		const itemsToInsert = [];
		const itemsToUpdate = [];

		for (const item of formattedData.insertArray) {
			try {
				let searchCriteria;
				if (service === this.clinicLabReportService) {
					searchCriteria = {
						name: (item as any).name,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.consumblesService) {
					searchCriteria = {
						productName: (item as any).productName,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.clinicServices) {
					searchCriteria = {
						serviceName: (item as any).serviceName,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.productsService) {
					searchCriteria = {
						productName: (item as any).productName,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.vaccinationService) {
					searchCriteria = {
						productName: (item as any).productName,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.clinicMedicationsService) {
					searchCriteria = {
						name: (item as any).name || (item as any).productName,
						clinicId: (item as any).clinicId
					};
				}

				this.logger.log(
					'Searching for existing item with criteria:',
					searchCriteria
				);
				const existingItem = await service.findOneEntry(searchCriteria);
				this.logger.log('Search result:', existingItem);

				if (existingItem) {
					this.logger.log(
						'Found existing item, will update:',
						existingItem
					);
					itemsToUpdate.push({ ...existingItem, ...item });
					summary.updated++;
				} else {
					this.logger.log('No existing item found, will create new');
					itemsToInsert.push(item);
					summary.created++;
				}
			} catch (error) {
				this.logger.error('Error processing item', { error, item });
				summary.failed++;
				errorArray.push({
					id:
						(item as any).uniqueId ||
						(item as any).name ||
						(item as any).productName,
					errors: [
						`Failed to process: ${(item as any).uniqueId || (item as any).name || (item as any).productName}`
					]
				});
			}
		}

		try {
			if (itemsToInsert.length > 0) {
				this.logger.log('Inserting new items', {
					count: itemsToInsert.length,
					items: itemsToInsert
				});
				await service.bulkInsert(itemsToInsert);
			}
			if (itemsToUpdate.length > 0) {
				this.logger.log('Updating existing items', {
					count: itemsToUpdate.length,
					items: itemsToUpdate
				});
				await service.bulkInsert(itemsToUpdate);
			}
		} catch (error) {
			this.logger.error('Error during bulk operation', { error });
			summary.failed += itemsToInsert.length + itemsToUpdate.length;
			summary.created = 0;
			summary.updated = 0;
			errorArray.push({
				id: 'bulk-operation',
				name: 'operation',
				errors: ['Failed to perform bulk operation']
			});
		}

		return {
			summary,
			errors: formattedData.errorArray,
			errorsUpdate: errorArray
		};
	}

	async generateInventoryExcel(clinicId: string): Promise<Buffer> {
		const clinic = await this.clinicRepository.findOne({
			where: { id: clinicId }
		});
		if (!clinic) {
			throw new NotFoundException(
				`Clinic with ID "${clinicId}" not found`
			);
		}

		const workbook = XLSX.utils.book_new();
		const createSheet = (
			data: any[],
			headers: string[],
			sheetName: string
		) => {
			const worksheet = XLSX.utils.aoa_to_sheet([]);
			XLSX.utils.sheet_add_aoa(worksheet, [headers], { origin: 'B2' });
			XLSX.utils.sheet_add_json(worksheet, data, {
				origin: 'B3',
				skipHeader: true
			});

			if (worksheet['!ref']) {
				const headerRange = XLSX.utils.decode_range(worksheet['!ref']);
				for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
					const cellRef = XLSX.utils.encode_cell({ r: 1, c: col });
					if (worksheet[cellRef]) {
						worksheet[cellRef].s = { font: { bold: true } };
					}
				}
			}

			const columnWidth = 20;
			worksheet['!cols'] = headers.map(() => ({ wch: columnWidth }));
			XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
		};

		const consumables =
			await this.consumblesService.getConsumables(clinicId);
		const consumablesHeaders = Object.values(ConsumableHeaders);
		const consumablesData = consumables.map(c => ({
			// [ConsumableHeaders.UNIQUE_ID]: c.uniqueId,
			[ConsumableHeaders.PRODUCT_NAME]: c.productName,
			[ConsumableHeaders.CURRENT_STOCK]: c.currentStock,
			[ConsumableHeaders.MINIMUM_QUANTITY]: c.minimumQuantity
		}));
		console.log(consumablesData, consumablesHeaders);
		createSheet(consumablesData, consumablesHeaders, 'Consumables');

		// Products
		const products = await this.productsService.getProducts(clinicId);
		const productsHeaders = Object.values(ProductHeaders);
		const productsData = products.map(p => ({
			// [ProductHeaders.UNIQUE_ID]: p.uniqueId,
			[ProductHeaders.PRODUCT_NAME]: p.productName,
			[ProductHeaders.CHARGEABLE_PRICE]: p.chargeablePrice,
			[ProductHeaders.TAX]: p.tax,
			[ProductHeaders.CURRENT_STOCK]: p.currentStock,
			[ProductHeaders.MINIMUM_QUANTITY]: p.minimumQuantity
		}));
		createSheet(productsData, productsHeaders, 'Products');

		// Services
		const services = await this.clinicServices.getServices(clinicId);
		const servicesHeaders = Object.values(ServiceHeaders);
		const servicesData = services.map(s => ({
			// [ServiceHeaders.UNIQUE_ID]: s.uniqueId,
			[ServiceHeaders.SERVICE_NAME]: s.serviceName,
			[ServiceHeaders.CHARGEABLE_PRICE]: s.chargeablePrice,
			[ServiceHeaders.TAX]: s.tax
		}));
		createSheet(servicesData, servicesHeaders, 'Services');

		// Lab Reports (Diagnostics)
		const labReports =
			await this.clinicLabReportService.getLabReports(clinicId);
		const filteredLabReports = labReports.filter(l => !l.integrationType);
		const labReportsHeaders = Object.values(DiagnosticHeaders);
		const labReportsData = filteredLabReports.map(l => ({
			[DiagnosticHeaders.SERVICE_NAME]: l.name,
			[DiagnosticHeaders.CHARGEABLE_PRICE]: l.chargeablePrice,
			[DiagnosticHeaders.TAX]: l.tax
		}));
		createSheet(labReportsData, labReportsHeaders, 'Diagnostics');

		// Vaccinations
		const vaccinations =
			await this.vaccinationService.getVaccinations(clinicId);
		const vaccinationsHeaders = Object.values(VaccinationHeaders);
		const vaccinationsData = vaccinations.map(v => ({
			// [VaccinationHeaders.UNIQUE_ID]: v.uniqueId,
			[VaccinationHeaders.PRODUCT_NAME]: v.productName,
			[VaccinationHeaders.CHARGEABLE_PRICE]: v.chargeablePrice,
			[VaccinationHeaders.TAX]: v.tax,
			[VaccinationHeaders.CURRENT_STOCK]: v.currentStock,
			[VaccinationHeaders.MINIMUM_QUANTITY]: v.minimumQuantity
		}));
		createSheet(vaccinationsData, vaccinationsHeaders, 'Vaccinations');

		// Medications
		const medications =
			await this.clinicMedicationsService.getMedications(clinicId);
		const medicationsHeaders = Object.values(MedicationHeaders);
		const medicationsData = medications.map(m => ({
			// [MedicationHeaders.UNIQUE_ID]: m.uniqueId,
			[MedicationHeaders.MEDICATION_NAME]: m.name,
			[MedicationHeaders.RESTRICTED_SUBSTANCE]: m.isRestricted,
			[MedicationHeaders.CHARGEABLE_PRICE]: m.chargeablePrice,
			[MedicationHeaders.TAX]: m.tax,
			[MedicationHeaders.CURRENT_STOCK]: m.currentStock,
			[MedicationHeaders.MINIMUM_QUANTITY]: m.minimumQuantity
		}));
		createSheet(medicationsData, medicationsHeaders, 'Medications');

		const excelBuffer = XLSX.write(workbook, {
			bookType: 'xlsx',
			type: 'buffer'
		});
		return excelBuffer;
	}

	async deleteInventoryItem(itemType: string, itemId: string) {
		let service;

		switch (itemType) {
			case 'consumables':
				service = this.consumblesService;
				break;
			case 'medications':
				service = this.clinicMedicationsService;
				break;
			case 'products':
				service = this.productsService;
				break;
			case 'services':
				service = this.clinicServices;
				break;
			case 'vaccinations':
				service = this.vaccinationService;
				break;
			case 'diagnostics':
				service = this.clinicLabReportService;
				break;
			default:
				throw new NotFoundException(`Invalid item type: ${itemType}`);
		}

		try {
			await service.deleteItem(itemId);
			return { message: `${itemType} item deleted successfully` };
		} catch (error) {
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new InternalServerErrorException(
				`Failed to delete ${itemType} item`
			);
		}
	}

	// Method to retrieve client booking settings, now returns the enhanced DTO
	async getClientBookingSettings(
		clinicId: string
	): Promise<ClientBookingSettingsResponseDto | null> {
		this.logger.log('Fetching client booking settings', { clinicId });
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id: clinicId }
			});

			if (!clinic) {
				this.logger.warn('Clinic not found for settings retrieval', {
					clinicId
				});
				throw new NotFoundException(
					`Clinic with ID "${clinicId}" not found`
				);
			}

			// Get the settings from the customRule JSONB field
			const settings = clinic.customRule?.clientBookingSettings;

			// If settings don't exist or are null, return null
			if (!settings) {
				this.logger.log(
					'Client booking settings not configured for clinic',
					{ clinicId }
				);
				return null;
			}

			// Prepare the base response object conforming to the DTO
			const response: ClientBookingSettingsResponseDto = {
				isEnabled: settings.isEnabled ?? false, // Provide default
				allowAllDoctors: settings.allowAllDoctors ?? false, // Default to false if null/undefined
				workingHours: settings.workingHours ?? undefined, // Use undefined if null
				allowedDoctorIds: settings.allowedDoctorIds ?? undefined,

				// Include new time duration fields
				minBookingLeadTime: settings.minBookingLeadTime ?? undefined,
				modificationDeadlineTime:
					settings.modificationDeadlineTime ?? undefined,
				maxAdvanceBookingTime:
					settings.maxAdvanceBookingTime ?? undefined,

				// Legacy fields
				minBookingLeadHours: settings.minBookingLeadHours ?? undefined,
				modificationDeadlineHours:
					settings.modificationDeadlineHours ?? undefined,
				allowedDoctorsInfo: [] // Initialize as empty array for consistency
			};

			// If allowedDoctorIds exist and the array is not empty, fetch doctor names
			if (
				settings.allowedDoctorIds &&
				settings.allowedDoctorIds.length > 0
			) {
				this.logger.log('Fetching doctor details for allowed IDs', {
					clinicId,
					ids: settings.allowedDoctorIds
				});
				try {
					const allowedClinicUsers =
						await this.clinicUserRepository.find({
							where: {
								clinicId: clinicId, // Ensure users belong to the correct clinic
								id: In(settings.allowedDoctorIds)
							},
							relations: ['user'], // Still need to load the related User entity
							select: {
								// Select necessary fields
								id: true,
								userId: true,
								user: {
									id: true,
									firstName: true,
									lastName: true
								}
							}
						});

					// Log the raw result from the database query
					this.logger.log(
						'Found clinic users from DB query (using ClinicUser ID):',
						{
							clinicId,
							allowedClinicUsers:
								JSON.stringify(allowedClinicUsers)
						}
					);

					// Map the found clinic users to the DoctorInfo structure
					// The response still needs the User ID and name
					response.allowedDoctorsInfo = allowedClinicUsers.map(
						cu => ({
							id: cu.userId, // Return the actual User ID
							// Construct the full name
							name:
								`${cu.user?.firstName ?? ''} ${cu.user?.lastName ?? ''}`.trim() ||
								'Name Unavailable'
						})
					);

					this.logger.log('Successfully fetched doctor details', {
						clinicId,
						count: response.allowedDoctorsInfo.length
					});
				} catch (userQueryError) {
					// Log the error but potentially continue without doctor names if that's acceptable
					this.logger.error(
						'Error fetching doctor details for client booking settings',
						{
							clinicId,
							allowedDoctorIds: settings.allowedDoctorIds,
							error: userQueryError
						}
					);
					// Decide if you want to throw or return partial data. Here, we'll return without doctor names.
					response.allowedDoctorsInfo = []; // Or undefined, depending on desired behavior on error
				}
			} else {
				this.logger.log(
					'No allowedDoctorIds specified or array is empty',
					{
						clinicId
					}
				);
				// Ensure allowedDoctorsInfo is an empty array if no IDs are provided
				// response.allowedDoctorsInfo = []; // Already initialized above
			}

			// Return the populated response object
			return response;
		} catch (error) {
			this.logger.error('Error fetching client booking settings', {
				clinicId,
				error
			});
			// Re-throw known exceptions
			if (
				error instanceof NotFoundException ||
				error instanceof InternalServerErrorException
			) {
				throw error;
			}
			// Throw a generic server error for other unexpected issues
			throw new InternalServerErrorException(
				'An unexpected error occurred while retrieving client booking settings'
			);
		}
	}

	// Method to update client booking settings
	async updateClientBookingSettings(
		clinicId: string,
		dto: UpdateClientBookingSettingsDto,
		updatedBy: string
	): Promise<ClinicEntity> {
		this.logger.log('Updating client booking settings', {
			clinicId,
			dto,
			updatedBy
		});
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id: clinicId }
			});

			if (!clinic) {
				this.logger.warn('Clinic not found for settings update', {
					clinicId
				});
				throw new NotFoundException(
					`Clinic with ID "${clinicId}" not found`
				);
			}

			// Get existing customRule or initialize with default structure if null/undefined
			const currentCustomRule = clinic.customRule || {
				patientLastNameAsOwnerLastName: false, // Keep existing or default value
				clientBookingSettings: null
			};

			// Get existing settings or initialize an empty object if null/undefined
			// Provide a default structure conforming to ClientBookingSettings if null
			const currentSettings: ClientBookingSettings =
				currentCustomRule.clientBookingSettings || {
					isEnabled: false,
					allowAllDoctors: false, // Default for new field
					workingHours: null,
					allowedDoctorIds: null,
					minBookingLeadTime: null,
					modificationDeadlineTime: null,
					maxAdvanceBookingTime: null,
					minBookingLeadHours: null,
					modificationDeadlineHours: null
				};

			// Merge the DTO into the current settings
			// Ensure the result conforms to ClientBookingSettings
			const mergedSettings: ClientBookingSettings = {
				isEnabled:
					dto.isEnabled !== undefined
						? dto.isEnabled
						: currentSettings.isEnabled,
				allowAllDoctors:
					dto.allowAllDoctors !== undefined
						? dto.allowAllDoctors
						: currentSettings.allowAllDoctors,
				workingHours:
					dto.workingHours !== undefined
						? dto.workingHours
						: currentSettings.workingHours,
				allowedDoctorIds:
					dto.allowedDoctorIds !== undefined
						? dto.allowedDoctorIds
						: currentSettings.allowedDoctorIds,

				// New time duration fields
				minBookingLeadTime:
					dto.minBookingLeadTime !== undefined
						? dto.minBookingLeadTime
						: currentSettings.minBookingLeadTime,
				modificationDeadlineTime:
					dto.modificationDeadlineTime !== undefined
						? dto.modificationDeadlineTime
						: currentSettings.modificationDeadlineTime,
				maxAdvanceBookingTime:
					dto.maxAdvanceBookingTime !== undefined
						? dto.maxAdvanceBookingTime
						: currentSettings.maxAdvanceBookingTime,

				// Legacy fields for backward compatibility
				minBookingLeadHours:
					dto.minBookingLeadHours !== undefined
						? dto.minBookingLeadHours
						: currentSettings.minBookingLeadHours,
				modificationDeadlineHours:
					dto.modificationDeadlineHours !== undefined
						? dto.modificationDeadlineHours
						: currentSettings.modificationDeadlineHours
			};

			// If allowAllDoctors is true, force allowedDoctorIds to null
			if (mergedSettings.allowAllDoctors === true) {
				mergedSettings.allowedDoctorIds = null;
				this.logger.log(
					'allowAllDoctors is true, setting allowedDoctorIds to null',
					{ clinicId }
				);
			}

			// Update the customRule object
			currentCustomRule.clientBookingSettings = mergedSettings;

			// Assign the updated rule back to the clinic entity
			clinic.customRule = currentCustomRule;
			clinic.updatedBy = updatedBy; // Set who updated the record

			// Save the updated clinic entity
			const updatedClinic = await this.clinicRepository.save(clinic);
			this.logger.log('Client booking settings updated successfully', {
				clinicId
			});
			return updatedClinic;
		} catch (error) {
			this.logger.error('Error updating client booking settings', {
				clinicId,
				dto,
				error
			});
			if (error instanceof NotFoundException) {
				throw error;
			}
			// Consider specific error handling for JSON serialization if needed, though TypeORM handles JSONB well
			throw new InternalServerErrorException(
				'Failed to update client booking settings'
			);
		}
	}

	// Method to get clinic settings
	async getClinicSettings(
		clinicId: string
	): Promise<ClinicSettingsResponseDto> {
		this.logger.log('Fetching clinic settings', { clinicId });
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id: clinicId }
			});

			if (!clinic) {
				this.logger.warn('Clinic not found for settings retrieval', {
					clinicId
				});
				throw new NotFoundException(
					`Clinic with ID "${clinicId}" not found`
				);
			}

			// Get the settings from the customRule JSONB field
			const customRule = clinic.customRule || {};

			// Return settings with defaults if not set
			return {
				patientLastNameAsOwnerLastName:
					customRule.patientLastNameAsOwnerLastName ?? false,
				defaultPatientList: customRule.defaultPatientList ?? 'all',
				appointmentBookingList:
					customRule.appointmentBookingList ?? 'alive'
			};
		} catch (error) {
			this.logger.error('Error fetching clinic settings', {
				clinicId,
				error
			});
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to fetch clinic settings'
			);
		}
	}

	// Method to update clinic settings
	async updateClinicSettings(
		clinicId: string,
		settingsDto: ClinicSettingsDto,
		updatedBy: string
	): Promise<ClinicSettingsResponseDto> {
		this.logger.log('Updating clinic settings', {
			clinicId,
			settingsDto,
			updatedBy
		});
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id: clinicId }
			});

			if (!clinic) {
				this.logger.warn('Clinic not found for settings update', {
					clinicId
				});
				throw new NotFoundException(
					`Clinic with ID "${clinicId}" not found`
				);
			}

			// Get existing customRule or initialize with default structure
			const currentCustomRule = clinic.customRule || {
				patientLastNameAsOwnerLastName: false,
				defaultPatientList: 'all',
				appointmentBookingList: 'alive'
			};

			// Update only the provided settings
			if (settingsDto.patientLastNameAsOwnerLastName !== undefined) {
				currentCustomRule.patientLastNameAsOwnerLastName =
					settingsDto.patientLastNameAsOwnerLastName;
			}
			if (settingsDto.defaultPatientList !== undefined) {
				currentCustomRule.defaultPatientList =
					settingsDto.defaultPatientList;
			}
			if (settingsDto.appointmentBookingList !== undefined) {
				currentCustomRule.appointmentBookingList =
					settingsDto.appointmentBookingList;
			}

			// Update the customRule object
			clinic.customRule = currentCustomRule;
			clinic.updatedBy = updatedBy;

			// Save the updated clinic entity
			await this.clinicRepository.save(clinic);
			this.logger.log('Clinic settings updated successfully', {
				clinicId
			});

			// Return the updated settings with defaults
			return {
				patientLastNameAsOwnerLastName:
					currentCustomRule.patientLastNameAsOwnerLastName ?? false,
				defaultPatientList:
					currentCustomRule.defaultPatientList ?? 'all',
				appointmentBookingList:
					currentCustomRule.appointmentBookingList ?? 'alive'
			};
		} catch (error) {
			this.logger.error('Error updating clinic settings', {
				clinicId,
				settingsDto,
				error
			});
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to update clinic settings'
			);
		}
	}
}

```


### 📁 `ui/app/brands/page.tsx`

**Lines:** 863 | **Size:** 32323 bytes

```typescript
'use client';

import React, { useState } from 'react';
import { Alert, Button, Heading } from '../atoms';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { Modal, Table } from '../molecules';
import { PaginationType } from '../molecules/Table';
import { useCreateBrand, useGetAllBrands } from '../services/brand.queries';
import ConfirmModal from '../organisms/patient/ConfirmModal';
import { AlertT } from '../atoms/Alert';
import classNames from 'classnames';
import CreateBrand from '../organisms/brand/CreateBrand';
import moment, { MomentInput } from 'moment';
import CreateClinic from '../organisms/brand/CreateClinic';
import {
    useCreateClinic,
    useDeactivateClinicMutation,
    useGetAllClinics,
    useReactivateClinicMutation,
    useSoftDeleteClinicMutation,
} from '../services/clinic.queries';
import Tabs from '../molecules/Tabs';
import IconAdd from '../atoms/customIcons/IconAdd.svg';
import ViewClinicDetails from '../organisms/brand/ViewClinicDetail';
import ViewBrandDetails from '../organisms/brand/ViewBrandDetail';
import EditClinicForm from '../organisms/brand/EditClinicDetail';
import EditBrandForm from '../organisms/brand/EditBrandDetail';

export interface ClinicFormData {
    name: string;
    brandId: string;
    adminFirstName: string;
    adminLastName: string;
    adminEmail: string;
    adminMobile: string;
}

export default function BrandsPage() {
    const [isBrandsModalOpen, setIsBrandsModalOpen] = useState(false);
    const [isClinicModalOpen, setIsClinicModalOpen] = useState(false);
    const [viewClinicModal, setViewClinicModal] = useState(false);
    const [editClinicModal, setEditClinicModal] = useState(false);
    const [viewBrandModal, setViewBrandModal] = useState(false);
    const [editBrandModal, setEditBrandModal] = useState(false);
    const [clinicId, setClinicId] = useState('');
    const [brandId, setBrandId] = useState('');
    const [selectedClinic, setSelectedClinic] = useState(null);
    const [selectedBrand, setSelectedBrand] = useState(null);

    const [confirmModal, setConfirmModal] = useState<{
        isOpen: boolean;
        clinicId: string | null;
        type?: 'deactivate' | 'reactivate' | 'delete';
    }>({
        isOpen: false,
        clinicId: null,
    });
    const [showAlert, setShowAlert] = useState<AlertT>({
        isOpen: false,
        label: '',
        variant: 'success',
    });

    // Pagination state management
    const limit = 10;
    const [brandsPagination, setBrandsPagination] = useState<PaginationType>({
        pageIndex: 0,
        pageSize: limit,
    });
    const [clinicsPagination, setClinicsPagination] = useState<PaginationType>({
        pageIndex: 0,
        pageSize: limit,
    });

    const createBrandValidationSchema = yup.object().shape({
        brandName: yup
            .string()
            .required('Brand Name is required')
            .max(50, 'Brand Name cannot exceed 50 characters'),
    });

    const createClinicValidationSchema = yup.object().shape({
        name: yup
            .string()
            .required('Clinic Name is required')
            .max(50, 'Clinic Name cannot exceed 50 characters'),
        brandId: yup.string().required('Brand Name is required'),
        adminFirstName: yup
            .string()
            .required('Admin First Name is required')
            .max(50, 'Admin First Name cannot exceed 50 characters'),
        adminLastName: yup
            .string()
            .required('Admin Last Name is required')
            .max(50, 'Admin Last Name cannot exceed 50 characters'),
        adminEmail: yup
            .string()
            .required('Admin Email is required')
            .email('Invalid Email'),
        adminMobile: yup
            .string()
            .required('Admin Mobile is required')
            .matches(
                /^[0-9]+$/,
                'Invalid Mobile Number. Mobile number should contain only numbers'
            )
            .min(10, 'Mobile number should be of 10 digits')
            .max(10, 'Mobile number should be of 10 digits'),
    });

    const {
        control,
        handleSubmit,
        setValue,
        getValues,
        watch,
        formState: { errors },
        reset,
    } = useForm({
        defaultValues: {
            brandName: '',
        },
        resolver: yupResolver(createBrandValidationSchema),
        mode: 'onChange',
    });

    const {
        control: controlClinic,
        handleSubmit: handleSubmitClinic,
        setValue: setValueClinic,
        getValues: getValuesClinic,
        watch: watchClinic,
        formState: { errors: clinicErrors },
        reset: resetClinic,
    } = useForm<ClinicFormData>({
        defaultValues: {
            name: '',
            brandId: '',
            adminFirstName: '',
            adminLastName: '',
            adminEmail: '',
            adminMobile: '',
        },
        resolver: yupResolver(createClinicValidationSchema),
        mode: 'onChange',
    });

    const { mutate: createClinic } = useCreateClinic();

    const {
        mutate: createBrand,
        isSuccess,
        isError,
        error,
        data,
    } = useCreateBrand();

    const { data: brandsData, status: brandsStatus } = useGetAllBrands(
        brandsPagination.pageIndex + 1,
        brandsPagination.pageSize,
        'DESC'
    );
    const { data: clinicsData, status: clinicsStatus } = useGetAllClinics(
        clinicsPagination.pageIndex + 1,
        clinicsPagination.pageSize,
        'DESC'
    );

    const deactivateClinicMutation = useDeactivateClinicMutation();
    const reactivateClinicMutation = useReactivateClinicMutation();
    const softDeleteClinicMutation = useSoftDeleteClinicMutation();

    const handleDeactivateClinic = (id: string) => {
        setConfirmModal({
            isOpen: true,
            clinicId: id,
            type: 'deactivate',
        });
    };

    const handleReactivateClinic = (id: string) => {
        setConfirmModal({
            isOpen: true,
            clinicId: id,
            type: 'reactivate',
        });
    };

    const handleSoftDeleteClinic = (id: string) => {
        setConfirmModal({
            isOpen: true,
            clinicId: id,
            type: 'delete',
        });
    };

    const handleConfirmAction = () => {
        if (!confirmModal.clinicId || !confirmModal.type) return;

        const { clinicId, type } = confirmModal;

        switch (type) {
            case 'deactivate':
                deactivateClinicMutation.mutate(clinicId, {
                    onSuccess: () => {
                        setConfirmModal({ isOpen: false, clinicId: null });
                        setShowAlert({
                            isOpen: true,
                            label: 'Clinic deactivated successfully!',
                            variant: 'success',
                        });
                    },
                    onError: () => {
                        setShowAlert({
                            isOpen: true,
                            label: 'Failed to deactivate clinic.',
                            variant: 'error',
                        });
                    },
                });
                break;

            case 'reactivate':
                reactivateClinicMutation.mutate(clinicId, {
                    onSuccess: () => {
                        setConfirmModal({ isOpen: false, clinicId: null });
                        setShowAlert({
                            isOpen: true,
                            label: 'Clinic reactivated successfully!',
                            variant: 'success',
                        });
                    },
                    onError: () => {
                        setShowAlert({
                            isOpen: true,
                            label: 'Failed to reactivate clinic.',
                            variant: 'error',
                        });
                    },
                });
                break;

            case 'delete':
                softDeleteClinicMutation.mutate(clinicId, {
                    onSuccess: () => {
                        setConfirmModal({ isOpen: false, clinicId: null });
                        setShowAlert({
                            isOpen: true,
                            label: 'Clinic deleted successfully!',
                            variant: 'success',
                        });
                    },
                    onError: () => {
                        setShowAlert({
                            isOpen: true,
                            label: 'Failed to delete clinic.',
                            variant: 'error',
                        });
                    },
                });
                break;
        }
    };

    React.useEffect(() => {
        if (isSuccess) {
            if (data.status === false && data.statusCode === 409) {
                setShowAlert({
                    isOpen: true,
                    label: 'Brand with this name already exists!',
                    variant: 'error',
                });
            } else if (data.status === true) {
                setShowAlert({
                    isOpen: true,
                    label: 'Brand created successfully!',
                    variant: 'success',
                });
            } else {
                setShowAlert({
                    isOpen: true,
                    label: 'Failed to create brand.',
                    variant: 'error',
                });
            }
            reset();
            setIsBrandsModalOpen(false);
        } else if (isError) {
            setShowAlert({
                isOpen: true,
                label: 'Failed to create brand.',
                variant: 'error',
            });
        }
    }, [isSuccess, isError, data, error]);

    const handleCreateBrand = (data: { brandName: string }) => {
        createBrand(data.brandName);
    };

    const handleCreateClinic = (data: ClinicFormData) => {
        createClinic(data, {
            onSuccess: (data) => {
                if (data.status === false && data.statusCode === 409) {
                    setShowAlert({
                        isOpen: true,
                        label: 'Clinic with this name already exists!',
                        variant: 'error',
                    });
                } else if (data.status === false && data.statusCode === 400) {
                    setShowAlert({
                        isOpen: true,
                        label: 'An Admin with this email already exists',
                        variant: 'error',
                    });
                } else if (data.status === true) {
                    setShowAlert({
                        isOpen: true,
                        label: 'Clinic created successfully!',
                        variant: 'success',
                    });
                } else {
                    setShowAlert({
                        isOpen: true,
                        label: 'Failed to create clinic.',
                        variant: 'error',
                    });
                }
                resetClinic();
                setIsClinicModalOpen(false);
            },
        });
    };

    const handleViewClinic = (id: string) => {
        const clinic = clinicsData?.data?.clinics?.find(
            (clinic: { id: string }) => clinic.id === id
        );

        if (clinic) {
            setSelectedClinic({
                ...clinic,
                brandName: clinic.brand?.name || 'Unknown Brand',
            });
            setClinicId(id);
            setViewClinicModal(true);
        } else {
            console.error('Clinic not found');
        }
    };

    const handleEditClinic = (id: string) => {
        const clinic = clinicsData?.data?.clinics?.find(
            (clinic: { id: string }) => clinic.id === id
        );

        if (clinic) {
            setSelectedClinic({
                ...clinic,
                brandName: clinic.brand?.name || 'Unknown Brand',
            });
            setClinicId(id);
            setEditClinicModal(true);
        } else {
            console.error('Clinic not found');
        }
    };

    const handleViewBrand = (id: string) => {
        const brand = brandsData?.data?.brands?.find(
            (brand: { id: string }) => brand.id === id
        );
        if (brand) {
            setSelectedBrand(brand);
            setBrandId(id);
            setViewBrandModal(true);
        }
    };

    const handleEditBrand = (id: string) => {
        const brand = brandsData?.data?.brands?.find(
            (brand: { id: string }) => brand.id === id
        );
        if (brand) {
            setSelectedBrand(brand);
            setBrandId(id);
            setEditBrandModal(true);
        }
    };

    const handleClinicRowClick = (rowData: any) => {
        const clinic = rowData.original;
        if (clinic?.brand?.slug) {
            // Open clinic in new tab using brand slug
            const clinicUrl = `https://${clinic.brand.slug}.nidana.io`;
            window.open(clinicUrl, '_blank');
        } else {
            console.error('Brand slug not found for clinic:', clinic);
        }
    };

    const columnHelper = createColumnHelper<any>();

    const clinicColumns: ColumnDef<any, any>[] = [
        columnHelper.accessor('name', {
            header: 'Clinic Name',
            cell: (info) => (
                <div>{String(info.getValue()) || 'Unknown Clinic'}</div>
            ),
        }),
        columnHelper.accessor(
            (row) => `${row.adminFirstName ?? ''} ${row.adminLastName ?? ''}`,
            {
                id: 'adminFullName',
                header: 'Admin Name',
                cell: (info) => <div>{info.getValue() || '--'}</div>,
            }
        ),
        columnHelper.accessor('adminEmail', {
            header: 'Admin Email',
            size: 20,
            meta: {
                tdClassName: 'max-w-[223px]',
                thClassName: 'max-w-[223px]',
            },
            cell: (info) => <div>{info.getValue() || '--'}</div>,
        }),
        columnHelper.accessor('adminMobile', {
            header: 'Admin Mobile',
            cell: (info) => <div>{info.getValue() || '--'}</div>,
        }),
        columnHelper.accessor('createdAt', {
            header: 'Created At',
            cell: (info) => (
                <div>
                    {moment(info.getValue() as MomentInput)?.format(
                        'DD MMM YYYY'
                    )}
                </div>
            ),
        }),
        columnHelper.accessor('brand', {
            header: 'Brand',
            cell: (info) => {
                const brand = info.getValue();
                return (
                    <div>
                        <div>{brand?.name || 'Unknown Brand'}</div>
                        {brand?.slug && (
                            <div className="text-xs text-gray-500">
                                {brand.slug}.nidana.io
                            </div>
                        )}
                    </div>
                );
            },
        }),
        columnHelper.accessor('isActive', {
            header: 'Status',
            cell: (info) => {
                const status = info.getValue();
                let displayStatus = '🔴️ Inactive';
                if (status == true) displayStatus = '🟢 Active';
                return <div>{displayStatus}</div>;
            },
        }),
        {
            id: 'action',
            header: 'Action',
            meta: {
                onActionClick: ({ row, action }) => {
                    if (action.id === 'edit') {
                        handleEditClinic(row.original.id);
                    }
                    if (action.id === 'view') {
                        handleViewClinic(row.original.id);
                    }
                    if (action.id === 'deactivate') {
                        handleDeactivateClinic(row.original.id);
                    }
                    if (action.id === 'reactivate') {
                        handleReactivateClinic(row.original.id);
                    }
                    if (action.id === 'delete') {
                        handleSoftDeleteClinic(row.original.id);
                    }
                },
                actionOptions: (row) => {
                    const isActive = row.original.isActive;
                    const baseOptions = [
                        {
                            id: 'edit',
                            label: 'Edit',
                        },
                        {
                            id: 'view',
                            label: 'View',
                        },
                    ];

                    // Show deactivate only for active clinics
                    if (isActive) {
                        baseOptions.push({
                            id: 'deactivate',
                            label: 'Deactivate',
                        });
                    } else {
                        // Show reactivate only for inactive clinics
                        baseOptions.push({
                            id: 'reactivate',
                            label: 'Reactivate',
                        });
                    }

                    // Always show delete option
                    baseOptions.push({
                        id: 'delete',
                        label: 'Delete',
                    });

                    return baseOptions;
                },
            },
        },
    ];
    const brandColumns: ColumnDef<any, any>[] = [
        columnHelper.accessor('name', {
            header: 'Brand Name',
            cell: (info) => (
                <div>{String(info.getValue()) || 'Unknown Clinic'}</div>
            ),
        }),
        columnHelper.accessor('createdAt', {
            header: 'Created At',
            cell: (info) => (
                <div>
                    {moment(info.getValue() as MomentInput)?.format(
                        'DD MMM YYYY'
                    )}
                </div>
            ),
        }),
        {
            id: 'action',
            header: 'Action',
            meta: {
                onActionClick: ({ row, action }) => {
                    if (action.id === 'edit') {
                        handleEditBrand(row.original.id);
                    }
                    if (action.id === 'view') {
                        handleViewBrand(row.original.id);
                    }
                },
                actionOptions: [
                    {
                        id: 'edit',
                        label: 'Edit',
                    },
                    {
                        id: 'view',
                        label: 'View',
                    },
                ],
            },
        },
    ];

    const brandOptions = async (search: string, _loadedOptions: unknown[]) => {
        const brands = brandsData?.data?.brands ?? [];
        const filteredBrands = brands.filter((item: any) =>
            item.name.toLowerCase().includes(search.toLowerCase())
        );

        const options = filteredBrands.map((item: any) => ({
            value: item.id,
            label: item.name,
        }));

        return {
            options,
            hasMore: false,
        };
    };

    // Helper functions for pagination data
    const getBrandsData = () => {
        return {
            pagination: brandsPagination,
            listLoadStatus: brandsStatus,
            setPagination: setBrandsPagination,
            tableData: brandsData?.data?.brands ?? [],
            totalPages: brandsData?.data?.total
                ? Math.ceil(brandsData.data.total / limit)
                : 1,
        };
    };

    const getClinicsData = () => {
        return {
            pagination: clinicsPagination,
            listLoadStatus: clinicsStatus,
            setPagination: setClinicsPagination,
            tableData: clinicsData?.data?.clinics ?? [],
            totalPages: clinicsData?.data?.total
                ? Math.ceil(clinicsData.data.total / limit)
                : 1,
        };
    };

    return (
        <div className="h-full w-full flex flex-col gap-4">
            <div className="w-full flex items-center justify-end">
                <div className="flex items-center justify-center gap-2">
                    <Button
                        icon
                        id="create-brand"
                        type="button"
                        variant="primary"
                        onClick={() => setIsBrandsModalOpen(true)}
                    >
                        <IconAdd size={16} />
                        <span>Create Brand</span>
                    </Button>
                    <Button
                        icon
                        id="create-brand"
                        type="button"
                        variant="primary"
                        onClick={() => setIsClinicModalOpen(true)}
                    >
                        <IconAdd size={16} />
                        <span>Create Clinic</span>
                    </Button>
                </div>
            </div>
            <div className="w-full flex items-center justify-between">
                <Heading
                    type="h4"
                    fontWeight="font-medium"
                    dataAutomation="verify-page-heading"
                >
                    Brands
                </Heading>
            </div>
            <div className="w-full">
                {brandsStatus === 'pending' ? (
                    <div>Loading...</div>
                ) : (
                    <Tabs
                        tabContentClass=" py-6 min-h-[400px] "
                        marginTabsTop="mt-5"
                        tabs={[
                            {
                                className: '',
                                id: 'clinics',
                                isDisabled: false,
                                label: 'Clinics',
                                tabContent: (
                                    <Table
                                        columns={clinicColumns}
                                        tableData={getClinicsData().tableData}
                                        pagination={getClinicsData().pagination}
                                        setPagination={
                                            getClinicsData().setPagination
                                        }
                                        isPaginationOutOfBox={true}
                                        customTableWrapperClass="h-[calc(100vh_-_260px)] overflow-auto"
                                        pageCount={getClinicsData().totalPages}
                                        listLoadStatus={
                                            getClinicsData().listLoadStatus
                                        }
                                        handleTableRowClick={
                                            handleClinicRowClick
                                        }
                                        emptyTableMessage={'No clinics added'}
                                        emptyTableButtonLabel="Add clinic"
                                        emptyTableButtonHandle={() =>
                                            setIsClinicModalOpen(true)
                                        }
                                        headerSticky={true}
                                        emptyStateHeight=""
                                    />
                                ),
                            },
                            {
                                className: '',
                                id: 'brands',
                                isDisabled: false,
                                label: 'Brands',
                                tabContent: (
                                    <Table
                                        columns={brandColumns}
                                        tableData={getBrandsData().tableData}
                                        pagination={getBrandsData().pagination}
                                        setPagination={
                                            getBrandsData().setPagination
                                        }
                                        isPaginationOutOfBox={true}
                                        customTableWrapperClass="h-[calc(100vh_-_260px)] overflow-auto"
                                        pageCount={getBrandsData().totalPages}
                                        listLoadStatus={
                                            getBrandsData().listLoadStatus
                                        }
                                        emptyTableMessage={'No brands added'}
                                        emptyTableButtonLabel="Add brand"
                                        emptyTableButtonHandle={() =>
                                            setIsBrandsModalOpen(true)
                                        }
                                        headerSticky={true}
                                        emptyStateHeight=""
                                    />
                                ),
                            },
                        ]}
                    />
                )}
            </div>
            {isBrandsModalOpen && (
                <CreateBrand
                    isOpen={isBrandsModalOpen}
                    onClose={() => setIsBrandsModalOpen(false)}
                    control={control}
                    errors={errors as { [key: string]: { message: string } }}
                    setValue={setValue}
                    getValues={getValues}
                    watch={watch}
                    handleCancel={() => setIsBrandsModalOpen(false)}
                    handleCreateBrand={handleCreateBrand}
                    handleSubmit={handleSubmit}
                />
            )}
            {isClinicModalOpen && (
                <CreateClinic
                    isOpen={isClinicModalOpen}
                    onClose={() => setIsClinicModalOpen(false)}
                    control={controlClinic}
                    errors={
                        clinicErrors as { [key: string]: { message: string } }
                    }
                    setValue={setValueClinic}
                    getValues={getValuesClinic}
                    watch={watchClinic}
                    handleCancel={() => setIsClinicModalOpen(false)}
                    handleSubmit={handleSubmitClinic}
                    handleCreateClinic={handleCreateClinic}
                    brandOptions={brandOptions}
                    brandsData={brandsData}
                />
            )}

            {confirmModal.isOpen && (
                <ConfirmModal
                    isOpen={confirmModal.isOpen}
                    alertType="warning"
                    modalDescription={
                        confirmModal.type === 'deactivate'
                            ? 'Are you sure you want to deactivate this clinic?'
                            : confirmModal.type === 'reactivate'
                              ? 'Are you sure you want to reactivate this clinic?'
                              : 'Are you sure you want to delete this clinic? This action cannot be undone.'
                    }
                    modalTitle={
                        confirmModal.type === 'deactivate'
                            ? 'Deactivate Clinic'
                            : confirmModal.type === 'reactivate'
                              ? 'Reactivate Clinic'
                              : 'Delete Clinic'
                    }
                    primaryBtnProps={{
                        label: 'Yes',
                        onClick: handleConfirmAction,
                        dataAutomation: `${confirmModal.type}-clinic-yes-button`,
                    }}
                    primaryBtnDisabled={false}
                    onClose={() =>
                        setConfirmModal({ isOpen: false, clinicId: null })
                    }
                    secondaryBtnProps={{
                        label: 'No',
                        onClick: () =>
                            setConfirmModal({ isOpen: false, clinicId: null }),
                        dataAutomation: `${confirmModal.type}-clinic-no-button`,
                    }}
                    dataAutomation={''}
                />
            )}

            {viewClinicModal && (
                <ViewClinicDetails
                    clinic={selectedClinic}
                    isOpen={viewClinicModal}
                    onClose={() => setViewClinicModal(false)}
                />
            )}

            {editClinicModal && (
                <Modal
                    isOpen={editClinicModal}
                    modalTitle="Edit Clinic Details"
                    onClose={() => setEditClinicModal(false)}
                    children={
                        <EditClinicForm
                            clinic={selectedClinic}
                            setModalOpen={setEditClinicModal}
                        />
                    }
                />
            )}

            {viewBrandModal && (
                <ViewBrandDetails
                    brand={selectedBrand}
                    isOpen={viewBrandModal}
                    onClose={() => setViewBrandModal(false)}
                />
            )}

            {editBrandModal && (
                <Modal
                    isOpen={true}
                    modalTitle="Edit Brand Details"
                    onClose={() => setEditBrandModal(false)}
                    children={
                        <EditBrandForm
                            brandId={brandId}
                            control={selectedBrand} // Pass the selected brand data
                            handleSubmit={(data: any) => {
                                // Call your update brand mutation here
                                console.log('Updated Brand:', data);
                            }}
                            errors={
                                errors as { [key: string]: { message: string } }
                            }
                            setValue={setValue}
                        />
                    }
                />
            )}

            <Alert
                className={classNames(
                    'font-semibold fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[560px] transition-opacity',
                    showAlert.isOpen
                        ? 'opacity-100 scale-100'
                        : 'opacity-0 scale-0'
                )}
                {...showAlert}
                onClose={() =>
                    setShowAlert({
                        isOpen: false,
                        label: '',
                        variant: 'success',
                    })
                }
            />
        </div>
    );
}

```


### 📁 `ui/app/components/Layout/BrandsLayout.tsx`

**Lines:** 48 | **Size:** 1713 bytes

```typescript
import React, { ReactNode, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { removeAuth } from '@/app/services/identity.service';
import { LogoutCurve } from 'iconsax-react';
import { IconText } from '@/app/atoms';

const BrandsLayout = ({ children }: { children: ReactNode }) => {
    const router = useRouter();

    const handleLogout = useCallback(
        (event: React.MouseEvent) => {
            event.preventDefault();
            removeAuth();
            router.push('/signin/pin');
        },
        [router]
    );

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Logout Button - positioned at top left */}
            <div className="absolute top-6 left-6 z-50">
                <button
                    data-automation="logout-brands-page"
                    onClick={handleLogout}
                    className="flex items-center gap-3 px-4 py-2 bg-primary-900 text-white rounded-lg hover:bg-primary-800 transition-colors duration-200 shadow-lg"
                >
                    <IconText
                        variant="bodySmall"
                        fontWeight="font-medium"
                        label="Log Out"
                        icon={<LogoutCurve size={20} />}
                        iconPosition="right"
                        classNameText="text-inherit"
                        className="gap-x-3"
                        textColor="text-inherit"
                    />
                </button>
            </div>

            {/* Main content */}
            <div className="p-6">{children}</div>
        </div>
    );
};

export default BrandsLayout;

```


### 📁 `ui/app/components/Layout/LayoutConfiguration.tsx`

**Lines:** 34 | **Size:** 923 bytes

```typescript
import { getAuth } from '@/app/services/identity.service';
import EmptyLayout from './EmptyLayout';
import PatientLayout from './PatientLayout';
import BrandsLayout from './BrandsLayout';

const getLayoutConfig = ({ pathname }: { pathname: string }) => {
    // Paths that should use EmptyLayout
    const emptyLayoutPaths = [
        '/signin/pin',
        '/signin/forgot-password',
        '/onboard',
        '/signin/email',
        '/signedDoc',
        '/',
    ];

    if (
        emptyLayoutPaths.some((path) => pathname === path) ||
        pathname.startsWith('/signedDoc/') ||
        pathname.startsWith('/estimate-signed-doc/')
    ) {
        return { Layout: EmptyLayout };
    }

    // Use BrandsLayout for brands page
    if (pathname === '/brands') {
        return { Layout: BrandsLayout };
    }

    return { Layout: PatientLayout };
};

export { getLayoutConfig };

```


### 📁 `ui/app/organisms/brand/ClinincDetail.tsx`

**Lines:** 156 | **Size:** 5167 bytes

```typescript
import { ClinicFormData } from '@/app/brands/page';
import RenderFields, { fieldsType } from '@/app/molecules/RenderFields';
import { Control, UseFormReturn } from 'react-hook-form';

interface ClinicDetailProps {
    control: Control<ClinicFormData>;
    errors: { [key: string]: { message: string } };
    setValue: UseFormReturn<ClinicFormData>['setValue'];
    getValues: UseFormReturn<ClinicFormData>['getValues'];
    watch: (name: keyof ClinicFormData) => any;
    brandOptions: (
        search: string,
        loadedOptions: unknown[]
    ) => Promise<{
        options: { value: any; label: string }[];
        hasMore: boolean;
    }>;
    brandsData: any;
}

const ClinicDetails: React.FC<ClinicDetailProps> = ({
    control,
    errors,
    setValue,
    watch,
    brandOptions,
    getValues,
    brandsData,
}) => {
    const fields: fieldsType[] = [
        {
            id: 'name',
            name: 'name',
            label: 'Clinic Name',
            placeholder: 'Enter clinic name',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().name,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('name', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
        {
            id: 'brandId',
            name: 'brandId',
            label: 'Brand Name',
            placeholder: 'Select brand',
            type: 'async-select',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: brandsData?.data?.brands?.find(
                (brand: any) => brand.id === getValues('brandId')
            )
                ? {
                      value: getValues('brandId'),
                      label: brandsData?.data?.brands?.find(
                          (brand: any) => brand.id === getValues('brandId')
                      )?.name,
                  }
                : null,
            loadOptions: brandOptions, // Function to load options
            onChange: (selectedOption: { value: string; label: string }) => {
                if (selectedOption) {
                    // Set only the value (ID) in the form state
                    setValue('brandId', selectedOption.value, {
                        shouldValidate: true,
                    });
                }
            },
        },
        {
            id: 'adminFirstName',
            name: 'adminFirstName',
            label: 'Admin First Name',
            placeholder: 'Enter Admin First name',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().adminFirstName,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('adminFirstName', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
        {
            id: 'adminLastName',
            name: 'adminLastName',
            label: 'Admin Last Name',
            placeholder: 'Enter admin last name',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().adminLastName,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('adminLastName', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
        {
            id: 'adminEmail',
            name: 'adminEmail',
            label: 'Admin Email',
            placeholder: 'Enter admin email',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().adminEmail,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('adminEmail', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
        {
            id: 'adminMobile',
            name: 'adminMobile',
            label: 'Admin Mobile Number',
            placeholder: 'Enter admin mobile',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().adminMobile,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('adminMobile', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
    ];

    return (
        <div className="w-full">
            <RenderFields
                control={control}
                errors={errors}
                fields={fields}
                setValue={setValue}
                watch={watch}
            />
        </div>
    );
};

export default ClinicDetails;

```


### 📁 `ui/app/services/brand.queries.ts`

**Lines:** 47 | **Size:** 1298 bytes

```typescript
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createBrand, getBrand, getBrands, getBrandsSimple } from './brands.services';

export function useCreateBrand() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createBrand,
        onSuccess: (data) => {
            if (data.status === true) {
                // Invalidate queries to reload the brands data
                queryClient.invalidateQueries({ queryKey: ['brands'] });
            }
        },
        onError: (error) => {
            console.error('Error creating brand:', error);
        },
    });
}


export function useGetAllBrands(
    page: number = 1,
    limit: number = 10,
    orderBy: string = 'DESC'
) {
    return useQuery({
        queryKey: ['brands', page, limit, orderBy],
        queryFn: () => getBrands(page, limit, orderBy),
    });
}

// Backward compatibility hook for non-paginated calls
export function useGetAllBrandsSimple() {
    return useQuery({
        queryKey: ['brands-simple'],
        queryFn: () => getBrandsSimple(),
    });
}

export function useGetBrand(id:string) {
    return useQuery({
        queryFn:()=> getBrand(id),
        queryKey: ['brands', id],
    });
}

```


### 📁 `ui/app/services/brands.services.ts`

**Lines:** 28 | **Size:** 835 bytes

```typescript
import * as HttpService from './http.service';
import { CREATE_BRAND, GET_BRAND, GET_BRAND_BY_SLUG, GET_BRANDS } from './url.service';

export const createBrand = (brandName: string) => {
    return HttpService.postWithAuth(CREATE_BRAND(brandName), {});
};

export const getBrands = (
    page: number = 1,
    limit: number = 10,
    orderBy: string = 'DESC'
) => {
    return HttpService.getWithAuth(GET_BRANDS(page, limit, orderBy));
};

// Backward compatibility method for non-paginated calls
export const getBrandsSimple = () => {
    return HttpService.getWithAuth(GET_BRANDS());
};

export const getBrand = (id: string) => {
    return HttpService.getWithOutAuth(GET_BRAND(id));
};

export const getBrandBySlug = (slug: string) => {
    return HttpService.getWithOutAuth(GET_BRAND_BY_SLUG(slug));
};

```


### 📁 `ui/app/services/clinic.queries.ts`

**Lines:** 933 | **Size:** 28583 bytes

```typescript
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
    addUserToClinic,
    createClinicConsumable,
    createClinicDiagnostic,
    createClinicMedication,
    createClinicProduct,
    createClinicRoom,
    createClinicService,
    createClinicUser,
    createClinicVaccination,
    createNewClinic,
    deactivateClinic,
    deleteClinicConsumable,
    deleteClinicDiagnostic,
    deleteClinicMedication,
    deleteClinicProduct,
    deleteClinicService,
    deleteClinicVaccination,
    deleteInventoryItem,
    deleteRoom,
    downloadLatestInventory,
    editClinic,
    getAllClinics,
    getAllClinicsSimple,
    getClinic,
    getClinicConsumables,
    getClinicDetails,
    getClinicDiagnostics,
    getClinicMedications,
    getClinicProducts,
    getClinicRooms,
    getClinicServices,
    getClinicVaccinations,
    getClinicWorkingHours,
    reactivateClinic,
    searchUsersAcrossClinics,
    softDeleteClinic,
    updateClinicConsumable,
    updateClinicDetails,
    updateClinicDiagnostic,
    updateClinicMedication,
    updateClinicProduct,
    updateClinicRoom,
    updateClinicService,
    updateClinicVaccination,
    updateClinicWorkingHours,
    uploadClinicExcel,
    getClientBookingSettings,
    updateClientBookingSettings,
    getClinicSettings,
    updateClinicSettings,
} from './clinic.service';
import { ClinicFormData } from '../brands/page';

// Interface for UpdateClinicDto
interface ClinicCustomRule {
    patientLastNameAsOwnerLastName: boolean;
}

// Clinic Settings Interfaces
export interface ClinicSettingsDto {
    patientLastNameAsOwnerLastName?: boolean;
    defaultPatientList?: 'all' | 'alive';
    appointmentBookingList?: 'all' | 'alive';
}

export interface ClinicSettingsResponseDto {
    patientLastNameAsOwnerLastName: boolean;
    defaultPatientList: 'all' | 'alive';
    appointmentBookingList: 'all' | 'alive';
}

// Temporary types to fix client booking settings (these were imported from @/lib/api)
interface ClientBookingWorkingHoursDto {
    [key: string]: any;
}

export interface UpdateClinicDto {
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    pin?: string;
    state?: string;
    country?: string;
    email?: string;
    website?: string;
    logoUrl?: string;
    drugLicenseNumber?: string;
    phoneNumbers?: string[];
    customRule?: ClinicCustomRule;
}

export interface ClinicResponse {
    id: string;
    name: string;
    customRule: ClinicCustomRule;
    // ... other fields
}

export interface EditClinicDto {
    name?: string;
    adminFirstName?: string;
    adminLastName?: string;
    adminEmail?: string;
    adminMobile?: string;
}

export interface UserFormData {
    firstName: string;
    lastName: string;
    role: 'Doctor' | 'Receptionist' | 'Lab Technician';
    email: string;
}

export interface CreateClinicRoomDto {
    name: string;
    clinicId: string;
}

export interface UpdateClinicRoomDto {
    name?: string;
    description?: string;
    clinicId?: string;
}
interface User {
    id: string;
    name: string;
    email: string;
    role: string;
    status: boolean;
}

interface UpdateClinicItemDto {
    id: string;
    data: any;
}

export const useClinicRooms = ({ clinicId }: { clinicId: string }) =>
    useQuery({
        queryKey: ['clinic-rooms', clinicId],
        queryFn: () => getClinicRooms(clinicId),
    });

export const useCreateClinicRoom = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, CreateClinicRoomDto>({
        mutationFn: (createClinicRoomDto) =>
            createClinicRoom(createClinicRoomDto),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinic-rooms'] });
        },
        onError: (error) => {
            console.error('Error creating clinic room:', error);
        },
    });
};

export const useUpdateClinicRoom = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, { id: string; data: UpdateClinicRoomDto }>({
        mutationFn: ({ id, data }) => updateClinicRoom(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinic-rooms'] });
        },
        onError: (error) => {
            console.error('Error updating clinic room:', error);
        },
    });
};

export const useDeleteRoom = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (roomId: string) => deleteRoom(roomId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({ queryKey: ['clinic-rooms'] });
        },
        onError: (error) => {
            console.error('Error deleting room:', error);
        },
    });
};

export const useUpdateClinicMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({
            clinicId,
            data,
        }: {
            clinicId: string;
            data: UpdateClinicDto;
        }) => updateClinicDetails(clinicId, data),
        onSuccess: (response, variables) => {
            queryClient.invalidateQueries({
                queryKey: ['clinicDetails', variables.clinicId],
            });
            queryClient.invalidateQueries({
                queryKey: ['clinicsDetails', variables.clinicId],
            });
        },
        onError: (error) => {
            console.error('Failed to update clinic details: ', error);
        },
    });
};
export const useEditClinicMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: string; data: EditClinicDto }) =>
            editClinic(id, data),
        onSuccess: (data) => {
            if (data.status === true) {
                queryClient.invalidateQueries({ queryKey: ['clinics'] });
            }
        },
        onError: (error) => {
            console.error('Error creating brand:', error);
        },
    });
};

export const useCreateClinicUserMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: ({
            clinicId,
            brandId,
            userData,
        }: {
            clinicId: string;
            brandId: string;
            userData: UserFormData;
        }) => createClinicUser(clinicId, brandId, userData),
        onSuccess: () => {
            // Invalidate and refetch
            //
            queryClient.invalidateQueries({ queryKey: ['clinicUsers'] });
        },
        onError: (error) => {
            console.error('Failed to create clinic users: ', error);
        },
    });
};

export const useSearchUsersAcrossClinics = () => {
    return useMutation({
        mutationFn: ({
            brandId,
            searchTerm,
            excludeClinicId,
        }: {
            brandId: string;
            searchTerm: string;
            excludeClinicId: string;
        }) => searchUsersAcrossClinics(brandId, searchTerm, excludeClinicId),
    });
};

export const useAddUserToClinic = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: ({
            userId,
            clinicId,
            brandId,
        }: {
            userId: string;
            clinicId: string;
            brandId: string;
        }) => addUserToClinic(userId, clinicId, brandId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: ['clinicUsers'],
            });
        },
    });
};

export const useExcelUpload = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: ({
            file,
            clinicId,
            brandId,
        }: {
            file: File;
            clinicId: string;
            brandId: string;
        }) => uploadClinicExcel(file, clinicId, brandId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: ['clinicConsumables'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicProducts'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicMedications'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicVaccinations'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicServices'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicDiagnostics'],
            });
        },
    });
};

export const useClinicConsumables = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicConsumables', clinicId, page, limit],
        queryFn: () => getClinicConsumables(clinicId, page, limit),
    });

export const useClinicProducts = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicProducts', clinicId, page, limit],
        queryFn: () => getClinicProducts(clinicId, page, limit),
    });

export const useClinicMedications = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicMedications', clinicId, page, limit],
        queryFn: () => getClinicMedications(clinicId, page, limit),
    });

export const useClinicVaccinations = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicVaccinations', clinicId, page, limit],
        queryFn: () => getClinicVaccinations(clinicId, page, limit),
    });

export const useClinicServices = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicServices', clinicId, page, limit],
        queryFn: () => getClinicServices(clinicId, page, limit),
    });

export const useClinicDiagnostics = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) =>
    useQuery({
        queryKey: ['clinicDiagnostics', clinicId, page, limit],
        queryFn: () => getClinicDiagnostics(clinicId, page, limit),
    });

export const useCreateClinicConsumable = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicConsumable,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicConsumables'] });
        },
    });
};

export const useCreateClinicProduct = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicProduct,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicProducts'] });
        },
    });
};

export const useCreateClinicMedication = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicMedication,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicMedications'] });
        },
    });
};

export const useCreateClinicVaccination = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicVaccination,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicVaccinations'] });
        },
    });
};

export const useCreateClinicService = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicService,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicServices'] });
        },
    });
};

export const useCreateClinicDiagnostic = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicDiagnostic,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicDiagnostics'] });
        },
    });
};

// Update clinic inventory
export const useUpdateClinicConsumable = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicConsumable(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicConsumables'] });
        },
        onError: (error) => {
            console.error('Error updating clinic consumable:', error);
        },
    });
};
export const useUpdateClinicProduct = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicProduct(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicProducts'] });
        },
        onError: (error) => {
            console.error('Error updating clinic product:', error);
        },
    });
};

export const useUpdateClinicMedication = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicMedication(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicMedications'] });
        },
        onError: (error) => {
            console.error('Error updating clinic medication:', error);
        },
    });
};

export const useUpdateClinicVaccination = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicVaccination(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicVaccinations'] });
        },
        onError: (error) => {
            console.error('Error updating clinic vaccination:', error);
        },
    });
};

export const useUpdateClinicService = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicService(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicServices'] });
        },
        onError: (error) => {
            console.error('Error updating clinic service:', error);
        },
    });
};

export const useUpdateClinicDiagnostic = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicDiagnostic(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicDiagnostics'] });
        },
        onError: (error) => {
            console.error('Error updating clinic diagnostic:', error);
        },
    });
};

// Delete clinic inventory
export const useDeleteClinicConsumable = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicConsumable(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicConsumables'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicProduct = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicProduct(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicProducts'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicMedication = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicMedication(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicMedications'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicVaccination = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicVaccination(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicVaccinations'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicService = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicService(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicServices'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicDiagnostic = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicDiagnostic(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicDiagnostics'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDownloadLatestInventory = () => {
    return useMutation({
        mutationFn: ({ clinicId }: { clinicId: string }) =>
            downloadLatestInventory(clinicId),
    });
};

export const useDeleteInventoryItem = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: ({
            itemType,
            itemId,
        }: {
            itemType: string;
            itemId: string;
        }) => deleteInventoryItem(itemType, itemId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: ['clinicConsumables'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicProducts'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicMedications'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicVaccinations'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicServices'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicDiagnostics'],
            });
        },
    });
};
export const useCreateClinic = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (clinicData: ClinicFormData) => createNewClinic(clinicData),
        onSuccess: (data) => {
            if (data.status === true) {
                // Invalidate the clinics query to refresh data after success
                queryClient.invalidateQueries({ queryKey: ['clinics'] });
            }
        },
        onError: (error: any) => {
            if (error?.response?.data?.message) {
                // If there's a specific error message from the backend
                console.error(
                    'Failed to create clinic:',
                    error.response.data.message
                );
            } else {
                // General fallback error message
                console.error(
                    'An unexpected error occurred while creating the clinic.'
                );
            }
        },
    });
};
export const useGetAllClinics = (
    page: number = 1,
    limit: number = 10,
    orderBy: string = 'DESC'
) => {
    return useQuery({
        queryKey: ['clinics', page, limit, orderBy],
        queryFn: () => getAllClinics(page, limit, orderBy),
    });
};

// Backward compatibility hook for non-paginated calls
export const useGetAllClinicsSimple = () => {
    return useQuery({
        queryKey: ['clinics-simple'],
        queryFn: () => getAllClinicsSimple(),
    });
};

export const useGetClinic = (id: string) => {
    return useQuery({
        queryFn: () => getClinic(id),
        queryKey: ['clinicsDetails', id],
    });
};
export const useDeactivateClinicMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: string) => {
            return deactivateClinic(id);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinics'] });
        },
        onError: (error) => {
            console.error('Failed to deactivate clinic: ', error);
        },
    });
};

export const useReactivateClinicMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: string) => {
            return reactivateClinic(id);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinics'] });
        },
        onError: (error) => {
            console.error('Failed to reactivate clinic: ', error);
        },
    });
};

export const useSoftDeleteClinicMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: string) => {
            return softDeleteClinic(id);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinics'] });
        },
        onError: (error) => {
            console.error('Failed to soft delete clinic: ', error);
        },
    });
};

export const useGetClinicWorkingHours = (clinicId: string) =>
    useQuery({
        queryKey: ['clinicWorkingHours', clinicId],
        queryFn: () => getClinicWorkingHours(clinicId),
    });

export const useUpdateClinicWorkingHours = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, { clinicId: string; data: any }>({
        mutationFn: ({ clinicId, data }) =>
            updateClinicWorkingHours(clinicId, data),
        onSuccess: (data, variables) => {
            // Invalidate relevant queries
            queryClient.invalidateQueries({
                queryKey: ['clinicWorkingHours', variables.clinicId],
            });
            queryClient.invalidateQueries({
                queryKey: ['clinicDetails', variables.clinicId],
            });
        },
        onError: (error) => {
            console.error('Error updating clinic working hours:', error);
        },
    });
};

export const useGetClinicDetails = (clinicId: string) =>
    useQuery({
        queryKey: ['clinicDetails', clinicId],
        queryFn: async () => {
            const response = await getClinicDetails(clinicId);
            return response;
        },
    });

// Time duration interface for granular time settings
export interface TimeDuration {
    days?: number | null;
    hours?: number | null;
    minutes?: number | null;
}

export interface UpdateClientBookingSettingsDto {
    isEnabled?: boolean;
    workingHours?: ClientBookingWorkingHoursDto;
    allowedDoctorIds?: string[] | null;
    allowAllDoctors?: boolean;

    // New time duration fields
    minBookingLeadTime?: TimeDuration | null;
    modificationDeadlineTime?: TimeDuration | null;
    maxAdvanceBookingTime?: TimeDuration | null;

    // Legacy fields for backward compatibility
    minBookingLeadHours?: number | null;
    modificationDeadlineHours?: number | null;
}

// Added DoctorInfo type based on API response and backend DTO
interface DoctorInfo {
    id: string;
    name: string;
}

export interface ClientBookingSettingsDto
    extends Omit<
        // Use Omit if Update DTO still has fields GET doesn't, otherwise just extend
        UpdateClientBookingSettingsDto,
        'minBookingLeadHours' | 'modificationDeadlineHours' // Example: Omit fields if GET structure differs significantly
    > {
    // Explicitly define all fields expected from the GET response
    isEnabled: boolean;
    workingHours?: ClientBookingWorkingHoursDto | null;
    allowedDoctorIds?: string[] | null;
    allowAllDoctors?: boolean;
    allowedDoctorsInfo?: DoctorInfo[] | null; // Added based on API response

    // New time duration fields
    minBookingLeadTime?: TimeDuration | null;
    modificationDeadlineTime?: TimeDuration | null;
    maxAdvanceBookingTime?: TimeDuration | null;

    // Legacy fields for backward compatibility
    minBookingLeadHours?: number | null;
    modificationDeadlineHours?: number | null;
}

// Query hook to get client booking settings
export const useGetClientBookingSettings = (clinicId: string) => {
    return useQuery<{
        data: ClientBookingSettingsDto;
        message: string;
        status: boolean;
    }>({
        // Specify the expected return type
        queryKey: ['clientBookingSettings', clinicId],
        queryFn: () => getClientBookingSettings(clinicId),
        enabled: !!clinicId, // Only run query if clinicId is truthy
        staleTime: 5 * 60 * 1000, // Optional: Cache for 5 minutes
        // Add error handling or other options as needed
    });
};

// Mutation hook to update client booking settings
export const useUpdateClientBookingSettingsMutation = () => {
    const queryClient = useQueryClient();

    return useMutation<
        any, // Adjust success response type if needed
        Error,
        { clinicId: string; data: UpdateClientBookingSettingsDto } // Variables type
    >({
        mutationFn: ({ clinicId, data }) =>
            updateClientBookingSettings(clinicId, data),
        onSuccess: (data, variables) => {
            // Invalidate the query for client booking settings to refetch
            queryClient.invalidateQueries({
                queryKey: ['clientBookingSettings', variables.clinicId],
            });
            // Optionally invalidate the general clinic details query too
            queryClient.invalidateQueries({
                queryKey: ['clinicDetails', variables.clinicId],
            });
            // Optionally add success notification/toast
        },
        onError: (error) => {
            console.error('Error updating client booking settings:', error);
            // Optionally add error notification/toast
        },
    });
};

// Define the structure based on your API DTOs
// Export DoctorDto if defined here, or ensure it's imported and re-exported
// If DoctorDto is defined within useGetClinicUsers, it needs to be moved out and exported.
// Assuming DoctorDto is meant to be shared:
// Corrected DoctorDto to represent a single doctor object
export interface DoctorDto {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    isActive: boolean;
    createdAt: string;
    role: {
        id: string;
        name: string;
        description: string;
    };
    userId: string;
    workingHours?: any;
}

// Clinic Settings React Query Hooks
export const useGetClinicSettings = (clinicId: string) => {
    return useQuery({
        queryKey: ['clinicSettings', clinicId],
        queryFn: () => getClinicSettings(clinicId),
        enabled: !!clinicId, // Only run query if clinicId is truthy
        staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    });
};

export const useUpdateClinicSettingsMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({
            clinicId,
            data,
        }: {
            clinicId: string;
            data: ClinicSettingsDto;
        }) => updateClinicSettings(clinicId, data),
        onSuccess: (data, variables) => {
            // Invalidate the query for clinic settings to refetch
            queryClient.invalidateQueries({
                queryKey: ['clinicSettings', variables.clinicId],
            });
            // Invalidate the general clinic details query too
            queryClient.invalidateQueries({
                queryKey: ['clinicDetails', variables.clinicId],
            });
        },
        onError: (error) => {
            console.error('Error updating clinic settings:', error);
        },
    });
};

```


### 📁 `ui/app/services/clinic.service.ts`

**Lines:** 404 | **Size:** 11589 bytes

```typescript
import * as Http from './http.service';
import {
    ADD_USER_TO_CLINIC,
    CREATE_CLINIC,
    CREATE_CLINIC_CONSUMABLES,
    CREATE_CLINIC_DIAGNOSTICS,
    CREATE_CLINIC_MEDICATIONS,
    CREATE_CLINIC_PRODUCTS,
    CREATE_CLINIC_ROOM,
    CREATE_CLINIC_SERVICES,
    CREATE_CLINIC_USER,
    CREATE_CLINIC_VACCINATIONS,
    DEACTIVATE_CLINIC,
    DELETE_CLINIC_CONSUMABLES,
    DELETE_CLINIC_DIAGNOSTICS,
    DELETE_CLINIC_MEDICATIONS,
    DELETE_CLINIC_PRODUCTS,
    DELETE_CLINIC_SERVICES,
    DELETE_CLINIC_VACCINATIONS,
    DELETE_INVENTORY_ITEM,
    DELETE_ROOM,
    DOWNLOAD_LATEST_INVENTORY,
    EDIT_CLINIC_DETAILS,
    GET_ALL_CLINICS,
    GET_CLINIC,
    GET_CLINIC_CONSUMABLES,
    GET_CLINIC_DETAILS,
    GET_CLINIC_DIAGNOSTICS,
    GET_CLINIC_MEDICATIONS,
    GET_CLINIC_PRODUCTS,
    GET_CLINIC_ROOMS,
    GET_CLINIC_SERVICES,
    GET_CLINIC_VACCINATIONS,
    GET_CLINIC_WORKING_HOURS,
    REACTIVATE_CLINIC,
    SOFT_DELETE_CLINIC,
    SEARCH_USERS_ACROSS_CLINICS,
    UPDATE_CLINIC_CONSUMABLES,
    UPDATE_CLINIC_DETAILS,
    UPDATE_CLINIC_DIAGNOSTICS,
    UPDATE_CLINIC_MEDICATIONS,
    UPDATE_CLINIC_PRODUCTS,
    UPDATE_CLINIC_ROOM,
    UPDATE_CLINIC_SERVICES,
    UPDATE_CLINIC_VACCINATIONS,
    UPDATE_CLINIC_WORKING_HOURS,
    UPLOAD_CLINIC_EXCEL,
    GET_CLIENT_BOOKING_SETTINGS,
    UPDATE_CLIENT_BOOKING_SETTINGS,
    GET_CLINIC_SETTINGS,
    UPDATE_CLINIC_SETTINGS,
} from './url.service';
import {
    CreateClinicRoomDto,
    EditClinicDto,
    UpdateClinicDto,
    UpdateClinicRoomDto,
    UserFormData,
    UpdateClientBookingSettingsDto,
} from './clinic.queries';
import { ClinicFormData } from '../brands/page';

export const getClinicRooms = (clinicId: string) => {
    return Http.getWithAuth(GET_CLINIC_ROOMS(clinicId));
};

export const createClinicRoom = (createClinicRoomDto: CreateClinicRoomDto) => {
    return Http.postWithAuth(CREATE_CLINIC_ROOM(), createClinicRoomDto);
};

export const updateClinicRoom = (
    id: string,
    updateClinicRoomDto: UpdateClinicRoomDto
) => {
    return Http.putWithAuth(UPDATE_CLINIC_ROOM(id), updateClinicRoomDto);
};

export const deleteRoom = (roomId: string) => {
    return Http.deleteWithAuth(DELETE_ROOM(roomId));
};

export const updateClinicDetails = async (
    clinicId: string,
    data: UpdateClinicDto
) => {
    const response = await Http.putWithAuth(
        UPDATE_CLINIC_DETAILS(clinicId),
        data
    );
    return response;
};

export const createClinicUser = (
    clinicId: string,
    brandId: string,
    userData: UserFormData
) => {
    return Http.postWithAuth(CREATE_CLINIC_USER(clinicId, brandId), userData);
};

export const searchUsersAcrossClinics = (
    brandId: string,
    searchTerm: string,
    excludeClinicId: string
) => {
    return Http.getWithAuth(
        SEARCH_USERS_ACROSS_CLINICS(brandId, searchTerm, excludeClinicId)
    );
};

export const addUserToClinic = (
    userId: string,
    clinicId: string,
    brandId: string
) => {
    return Http.postWithAuth(ADD_USER_TO_CLINIC(userId, clinicId, brandId), {});
};

export const uploadClinicExcel = (
    file: File,
    clinicId: string,
    brandId: string
) => {
    const formData = new FormData();
    formData.append('file', file);
    return Http.postWithAuth(UPLOAD_CLINIC_EXCEL(clinicId, brandId), formData);
};

export const getClinicConsumables = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_CONSUMABLES(clinicId, page, limit));
};

export const getClinicProducts = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_PRODUCTS(clinicId, page, limit));
};

export const getClinicMedications = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_MEDICATIONS(clinicId, page, limit));
};

export const getClinicVaccinations = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_VACCINATIONS(clinicId, page, limit));
};

export const getClinicServices = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_SERVICES(clinicId, page, limit));
};

export const getClinicDiagnostics = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_DIAGNOSTICS(clinicId, page, limit));
};

export const createClinicConsumable = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_CONSUMABLES(), body);
};

export const createClinicProduct = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_PRODUCTS(), body);
};

export const createClinicMedication = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_MEDICATIONS(), body);
};

export const createClinicVaccination = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_VACCINATIONS(), body);
};

export const createClinicService = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_SERVICES(), body);
};

export const createClinicDiagnostic = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_DIAGNOSTICS(), body);
};

// Update Functions for Consumables, Products, Medications, Vaccinations, Services, Diagnostics

export const updateClinicConsumable = (
    id: string,
    updateConsumableDto: any
) => {
    return Http.patchWithAuth(
        UPDATE_CLINIC_CONSUMABLES(id),
        updateConsumableDto
    );
};

export const updateClinicProduct = (id: string, updateProductDto: any) => {
    return Http.patchWithAuth(UPDATE_CLINIC_PRODUCTS(id), updateProductDto);
};

export const updateClinicMedication = (
    id: string,
    updateMedicationDto: any
) => {
    return Http.patchWithAuth(
        UPDATE_CLINIC_MEDICATIONS(id),
        updateMedicationDto
    );
};

export const updateClinicVaccination = (
    id: string,
    updateVaccinationDto: any
) => {
    return Http.patchWithAuth(
        UPDATE_CLINIC_VACCINATIONS(id),
        updateVaccinationDto
    );
};

export const updateClinicService = (id: string, updateServiceDto: any) => {
    return Http.patchWithAuth(UPDATE_CLINIC_SERVICES(id), updateServiceDto);
};

export const updateClinicDiagnostic = (
    id: string,
    updateDiagnosticDto: any
) => {
    return Http.patchWithAuth(
        UPDATE_CLINIC_DIAGNOSTICS(id),
        updateDiagnosticDto
    );
};

// Delete Functions for Consumables, Products, Medications, Vaccinations, Services, Diagnostics

export const deleteClinicConsumable = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_CONSUMABLES(id));
};

export const deleteClinicProduct = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_PRODUCTS(id));
};

export const deleteClinicMedication = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_MEDICATIONS(id));
};

export const deleteClinicVaccination = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_VACCINATIONS(id));
};

export const deleteClinicService = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_SERVICES(id));
};

export const deleteClinicDiagnostic = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_DIAGNOSTICS(id));
};

export const downloadLatestInventory = async (clinicId: string) => {
    const response = await Http.getWithAuthBlob(
        DOWNLOAD_LATEST_INVENTORY(clinicId)
    );
    return response;
};

export const deleteInventoryItem = async (
    itemType: string,
    itemId: string
): Promise<void> => {
    await Http.deleteWithAuth(DELETE_INVENTORY_ITEM(itemType, itemId));
};
export const createNewClinic = (clinicData: ClinicFormData) => {
    return Http.postWithAuth(CREATE_CLINIC(), clinicData);
};

export const getAllClinics = (
    page: number = 1,
    limit: number = 10,
    orderBy: string = 'DESC'
) => {
    return Http.getWithAuth(GET_ALL_CLINICS(page, limit, orderBy));
};

// Backward compatibility method for non-paginated calls
export const getAllClinicsSimple = () => {
    return Http.getWithAuth(GET_ALL_CLINICS());
};

export const getClinic = async (id: string) => {
    const response = await Http.getWithAuth(GET_CLINIC(id));
    return response;
};

export const editClinic = (id: string, data: EditClinicDto) => {
    return Http.putWithAuth(EDIT_CLINIC_DETAILS(id), data);
};

export const deactivateClinic = (id: string) => {
    return Http.putWithAuth(DEACTIVATE_CLINIC(id), {});
};

export const reactivateClinic = (id: string) => {
    return Http.putWithAuth(REACTIVATE_CLINIC(id), {});
};

export const softDeleteClinic = (id: string) => {
    return Http.deleteWithAuth(SOFT_DELETE_CLINIC(id));
};
export const getClinicWorkingHours = (clinicId: string) => {
    return Http.getWithAuth(GET_CLINIC_WORKING_HOURS(clinicId));
};

export const updateClinicWorkingHours = (
    clinicId: string,
    workingHours: any
) => {
    return Http.putWithAuth(
        UPDATE_CLINIC_WORKING_HOURS(clinicId),
        workingHours
    );
};

// Client Booking Settings Service Functions
/**
 * Fetches the client booking settings for a specific clinic.
 * @param clinicId - The ID of the clinic.
 * @returns Promise resolving to the client booking settings.
 */
export const getClientBookingSettings = (clinicId: string) => {
    if (!clinicId) {
        // Or handle this case appropriately
        return Promise.reject(new Error('Clinic ID is required'));
    }
    return Http.getWithAuth(GET_CLIENT_BOOKING_SETTINGS(clinicId));
};

/**
 * Updates the client booking settings for a specific clinic.
 * @param clinicId - The ID of the clinic.
 * @param data - The updated settings data (UpdateClientBookingSettingsDto).
 * @returns Promise resolving to the updated clinic data.
 */
export const updateClientBookingSettings = (
    clinicId: string,
    data: UpdateClientBookingSettingsDto // Use the defined DTO
) => {
    if (!clinicId) {
        // Or handle this case appropriately
        return Promise.reject(new Error('Clinic ID is required'));
    }
    return Http.putWithAuth(UPDATE_CLIENT_BOOKING_SETTINGS(clinicId), data);
};
// End Client Booking Settings Service Functions

// Clinic Settings Service Functions
/**
 * Fetches the clinic settings for a specific clinic.
 * @param clinicId - The ID of the clinic.
 * @returns Promise resolving to the clinic settings.
 */
export const getClinicSettings = (clinicId: string) => {
    if (!clinicId) {
        return Promise.reject(new Error('Clinic ID is required'));
    }
    return Http.getWithAuth(GET_CLINIC_SETTINGS(clinicId));
};

/**
 * Updates the clinic settings for a specific clinic.
 * @param clinicId - The ID of the clinic.
 * @param data - The updated settings data.
 * @returns Promise resolving to the updated clinic settings.
 */
export const updateClinicSettings = (clinicId: string, data: any) => {
    if (!clinicId) {
        return Promise.reject(new Error('Clinic ID is required'));
    }
    return Http.putWithAuth(UPDATE_CLINIC_SETTINGS(clinicId), data);
};
// End Clinic Settings Service Functions

export const getClinicDetails = async (clinicId: string) => {
    const url = GET_CLINIC_DETAILS(clinicId);
    return Http.getWithAuth(url);
};

```


### 📁 `ui/app/services/url.service.ts`

**Lines:** 1723 | **Size:** 52684 bytes

```typescript
import { AppointmentParams } from '../types/appointment';
import { GetPatientsT } from '../types/patient';
import { DoctorAvailabilityParams, GetDoctorsType } from '../types/provider';

const ApiUrl = process.env.NEXT_PUBLIC_API_URL;
type Params = { [key: string]: string | number };

const UrlParamsReplace = (
    url: string,
    pathParams: Params = {},
    queryParams: Params = {}
) => {
    let urlWithPrefix = `${ApiUrl}${url}`;

    // Replace path parameters
    if (pathParams) {
        Object.keys(pathParams).forEach(
            (key) =>
                (urlWithPrefix = urlWithPrefix.replace(
                    `:${key}`,
                    String(pathParams[key])
                ))
        );
    }

    // Add query parameters
    if (Object.keys(queryParams).length > 0) {
        const queryString = Object.entries(queryParams)
            .map(
                ([key, value]) =>
                    `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
            )
            .join('&');
        urlWithPrefix += `?${queryString}`;
    }

    return urlWithPrefix;
};

export const UPDATE_USER_ROUTE = () => UrlParamsReplace('/auth/user-route');

export const GET_CLINIC_PATIENTS = (
    page: number,
    limit: number,
    searchTerm: string,
    withBalance: string,
    patientStatus: string = 'all'
) =>
    UrlParamsReplace(
        '/patients',
        {},
        { page, limit, searchTerm, withBalance, patientStatus }
    );

export const GET_PATIENTS = ({ page, limit, clinicId, search }: GetPatientsT) =>
    UrlParamsReplace(
        '/patients/clinic/:clinicId',
        { clinicId },
        { page, limit, search }
    );

export const GET_PATIENT_DETAILS = (id: any) =>
    UrlParamsReplace('/patients/:id', { id }, {});

//appointments
export const CREATE_APPOINTMENT = () => UrlParamsReplace('/appointments');
export const GET_APPOINTMENTS = ({
    page,
    limit,
    orderBy,
    date,
    search,
    doctors,
    status,
    onlyPrimary,
}: AppointmentParams) =>
    UrlParamsReplace(
        '/appointments',
        {},
        {
            page,
            limit,
            orderBy,
            date,
            search,
            doctors: JSON.stringify(doctors),
            status: JSON.stringify(status),
            onlyPrimary: JSON.stringify(onlyPrimary),
        }
    );

export const SEARCH_PATIENT_BY_PHONE = (phoneNumber: string) =>
    UrlParamsReplace('/owners/search', {}, { phoneNumber });

export const CREATE_PATIENT = () => UrlParamsReplace('/patients');

export const UPDATE_PATIENT = (patientId: string) =>
    UrlParamsReplace('/patients/:id', { id: patientId });
//patient-alert
export const CREATE_PATIENT_ALERT = UrlParamsReplace('/patient-alert', {}, {});
export const DELETE_PATIENT_ALERT = (id: any, all?: string) =>
    UrlParamsReplace('/patient-alert/:id', { id }, { ...(all ? { all } : {}) });
export const GET_PATIENT_ALERT = (patientId: string) =>
    UrlParamsReplace('/patient-alert/:patientId', { patientId }, {});
export const UPDATE_PATIENT_ALERT = (id: string) =>
    UrlParamsReplace('/patient-alert/:id', { id }, {});

export const GET_CLINIC_DOCTORS = ({
    page,
    limit,
    role,
    clinicId,
    orderBy = 'ASC',
}: GetDoctorsType) => {
    return UrlParamsReplace(
        '/users/clinic/:clinicId',
        { clinicId },
        { page, limit, orderBy, ...(role && { role }) }
    );
};
export const GET_CLINIC_DOCTORS_AVAILABILITY = ({
    clinicId,
    date,
    startTime,
    endTime,
    role,
    orderBy = 'ASC',
}: DoctorAvailabilityParams) => {
    return UrlParamsReplace(
        '/users/clinic/availability/:clinicId',
        { clinicId },
        {
            orderBy,
            ...(date && { date }),
            ...(startTime && { startTime }),
            ...(endTime && { endTime }),
            ...(role && { role }),
        }
    );
};
// Update appointment details (SOAP)
export const UPDATE_APPOINTMENT_DETAILS = (appointmentId: string) => {
    return UrlParamsReplace('/appointments/:id/details', { id: appointmentId });
};

//clinics rooms
export const GET_CLINIC_ROOMS = (clinicId: string) => {
    return UrlParamsReplace('/clinics/:id/rooms', { id: clinicId });
};

export const CREATE_CLINIC_ROOM = () => UrlParamsReplace('/clinics/rooms');

export const UPDATE_CLINIC_ROOM = (id: string) =>
    UrlParamsReplace(`/clinics/rooms/${id}`);

export const DELETE_ROOM = (roomId: string) =>
    UrlParamsReplace(`/clinics/rooms/${roomId}`);

export const UPDATE_CLINIC_DETAILS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId', { clinicId });

export const CREATE_CLINIC_USER = (clinicId: string, brandId: string) =>
    UrlParamsReplace(`/users?clinicId=${clinicId}&brandId=${brandId}`);

export const GET_CLINIC_WORKING_HOURS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/working-hours', { clinicId });

export const UPDATE_CLINIC_WORKING_HOURS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/working-hours', { clinicId });

export const GET_USERS = (clinicId: string, page: number, limit: number) =>
    UrlParamsReplace('/users/clinic/:clinicId', { clinicId }, { page, limit });

export const UPDATE_USER_STATUS = (userId: string) =>
    UrlParamsReplace('/users/:userId/status', { userId });

export const GET_USER_DETAILS = (userId: string) =>
    UrlParamsReplace('/users/:userId', { userId });

export const UPDATE_USER_DETAILS = (userId: string) =>
    UrlParamsReplace('/users/clinic/:userId', { userId });

export const COMPLETE_STAFF_DETAILS = (globalUserId: string) =>
    UrlParamsReplace('/users/complete-profile/:globalUserId', { globalUserId });

export const GET_ALL_ROLES = () => UrlParamsReplace('/roles');

export const SEARCH_USERS_ACROSS_CLINICS = (
    brandId: string,
    searchTerm: string,
    excludeClinicId: string
) =>
    UrlParamsReplace(
        '/users/search',
        {},
        { brandId, searchTerm, excludeClinicId }
    );

export const ADD_USER_TO_CLINIC = (
    userId: string,
    clinicId: string,
    brandId: string
) =>
    UrlParamsReplace('/users/:userId/add-to-clinic/:clinicId', {
        userId,
        clinicId,
        brandId,
    });

// Appointment API
export const GET_LATEST_PATIENT_APPOINTMENTS = (patientId: string) =>
    UrlParamsReplace('/appointments/patients/:patientId', { patientId });

export const GET_APPOINTMENT_DETAILS = (appointmentId: string) =>
    UrlParamsReplace('/appointments/:appointmentId', { appointmentId });

// Update appointment
export const UPDATE_APPOINTMENT_STATUS = (appointmentId: string) =>
    UrlParamsReplace('/appointments/:appointmentId/status', { appointmentId });

export const CHECK_PATIENT_ONGOING_APPOINTMENT = (patientId: string) =>
    UrlParamsReplace(
        '/appointments/patients/:patientId/check-ongoing-appointment',
        { patientId }
    );
// Pin Login
export const LOGIN_WITH_PIN = () => UrlParamsReplace('/auth/login/pin', {});

export const RESET_PIN_URL = UrlParamsReplace('/auth/reset-pin');

export const VERIFY_PIN_URL = () => UrlParamsReplace('/auth/verify-pin', {});
// Email, Otp Login
type LoginWithEmailOtpParams = {
    email: string;
    otp: string;
};
export const GENERATE_OTP_FOR_EMAIL = () => UrlParamsReplace('/otp/generate');

export const LOGIN_WITH_EMAIL_OTP = () =>
    UrlParamsReplace('/auth/login/users/email');

// Get lab reports
export const GET_LAB_REPORTS = (search: string, clinicId: string) =>
    UrlParamsReplace('/clinic-lab-reports/types', { search }, { clinicId });

// Get assessment list
export const GET_ASSESSMENT_LIST = (search: string) => {
    return UrlParamsReplace('/appointment-assessment', { search });
};

// Post new assessment
export const CREATE_NEW_ASSESSMENT = () => {
    return UrlParamsReplace('/appointment-assessment');
};

// Get plan list
export const GET_PLAN_LIST = (
    search: string,
    exclude: string,
    clinicId: string
) => {
    const queryParams = new URLSearchParams({
        search: search,
        exclude: exclude,
        clinicId: clinicId,
    });

    return UrlParamsReplace(`/clinic-plans?${queryParams.toString()}`);
};

// Get medication list
export const GET_PRESCRIPTION_LIST = (
    search: string,
    clinicId: string,
    all: boolean = false
) => {
    return UrlParamsReplace(
        '/clinic-medications',
        {},
        { clinicId, all: all ? 'true' : 'false' }
    );
};

export const GET_DIAGNOSTIC_LIST = (search: string, clinicId: string) => {
    return UrlParamsReplace(
        '/clinic-lab-reports/types',
        { search },
        { clinicId }
    );
};
export const UPDATE_APPOINTMENT_FEILDS = (appointmentId: string) => {
    return UrlParamsReplace('/appointments/:appointmentId', { appointmentId });
};

export const DELETE_APPOINTMENT = (appointmentId: string) => {
    return UrlParamsReplace('/appointments/:appointmentId', { appointmentId });
};

export const GET_UPLOAD_PRE_SIGED_URL = () =>
    UrlParamsReplace('/aws-s3/signed-url');

export const CREATE_LAB_REPORT = (isCreate?: boolean) =>
    UrlParamsReplace(
        '/clinic-lab-reports/lab-report',
        {},
        isCreate !== undefined ? { isCreate: isCreate.toString() } : {}
    );

export const UPDATE_LAB_REPORT_STATUS = () =>
    UrlParamsReplace('/clinic-lab-reports/status');

export const GET_VIEW_PRE_SIGNED_URL = (fileKey: string) =>
    UrlParamsReplace(
        `/aws-s3/view-signed-url?fileKey=${encodeURIComponent(fileKey)}`
    );

export const DELETE_LAB_REPORT_FILE = (
    labReportId: string,
    fileKey: string,
    lineItemId: string
) =>
    UrlParamsReplace(
        `/clinic-lab-reports/${labReportId}/files/${encodeURIComponent(fileKey)}?lineItemId=${lineItemId}`
    );

export const DELETE_LAB_REPORT = (
    labReportId: string,
    appointmentId: string,
    lineItemId: string
) =>
    UrlParamsReplace(
        `/clinic-lab-reports/lab-report/${labReportId}?appointmentId=${appointmentId}&lineItemId=${lineItemId}`
    );

// export const GET_ALL_LAB_REPORTS = () => UrlParamsReplace('/clinic-lab-reports');

export const GET_DOWNLOAD_PRESIGNED_URL = (
    fileKey: string,
    fileType: 'img' | 'pdf' | '' = ''
) =>
    UrlParamsReplace(
        `/aws-s3/download-file?fileKey=${encodeURIComponent(fileKey)}&fileType=${fileType}`
    );

export const GET_ALL_LAB_REPORTS = (params: {
    clinicId: string;
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
    searchTerm?: string;
    status?: string;
}) => {
    const queryParams = new URLSearchParams({
        clinicId: params.clinicId,
        page: (params.page || 1).toString(),
        limit: (params.limit || 10).toString(),
        searchTerm: params.searchTerm || '',
        status: params.status || '',
    });

    if (params.startDate) {
        queryParams.append('startDate', params.startDate);
    }

    if (params.endDate) {
        queryParams.append('endDate', params.endDate);
    }

    return UrlParamsReplace(`/clinic-lab-reports?${queryParams.toString()}`);
};

export const GET_LAB_REPORTS_FOR_PATIENT = (patientId: string) =>
    UrlParamsReplace('/clinic-lab-reports/diagnostic/:patientId', {
        patientId,
    });

export const GET_LAB_REPORT_BY_ID = (labReportId: string) =>
    UrlParamsReplace('/clinic-lab-reports/lab-report/:id', {
        id: labReportId,
    });

// Post new medication/prescription
export const CREATE_NEW_PRESCRIPTION = () => {
    return UrlParamsReplace('/clinic-medications');
};

// Add long term prescription data
export const ADD_LONG_TERM_PRESCRIPTION = () => {
    return UrlParamsReplace('/long-term-medications');
};

// Delete long term prescription data
export const DELETE_LONG_TERM_PRESCRIPTION = (
    patientId: string,
    medicationId: string
) => {
    return UrlParamsReplace(
        '/long-term-medications',
        {},
        {
            patientId,
            medicationId,
        }
    );
};

// Get long term prescription data
export const GET_LONG_TERM_PRESCRIPTION = (patientId: string) => {
    return UrlParamsReplace(
        '/long-term-medications',
        {},
        {
            patientId,
        }
    );
};

// tasks
export const CREATE_TASK = UrlParamsReplace(`/tasks`);

export const GET_TASK = (userId: string) => {
    return UrlParamsReplace('/tasks/:userId', { userId });
};

export const DELETE_TASK = (id: string) => {
    return UrlParamsReplace('/tasks/:id', { id });
};

export const UPDATE_TASK = (id: string) => {
    return UrlParamsReplace('/tasks/:id', { id });
};

//users

export const GET_ALL_USER = UrlParamsReplace('/users');

//chats
export const GET_USER_CHAT_ROOMS = (userId: string) => {
    return UrlParamsReplace('/chat-rooms/user/:userId', { userId });
};

export const GET_CHAT_ROOM_DETAILS = (chatRoomId: string) => {
    return UrlParamsReplace('/chat-rooms/:id', { id: chatRoomId });
};

export const CREATE_CHAT_ROOM = UrlParamsReplace('/chat-rooms');
export const SEND_MESSAGE = UrlParamsReplace('/chat-rooms/message');
export const UPDATE_CHAT_ROOM = (id: string) =>
    UrlParamsReplace('/chat-rooms/:id', { id });

// Brands
export const CREATE_BRAND = (name: string) =>
    UrlParamsReplace('/brands', {}, { name });

export const GET_BRANDS = (
    page?: number,
    limit?: number,
    orderBy?: string
) => {
    const params: any = {};
    if (page !== undefined) params.page = page;
    if (limit !== undefined) params.limit = limit;
    if (orderBy !== undefined) params.orderBy = orderBy;

    return UrlParamsReplace('/brands', {}, params);
};

export const GET_BRAND = (id: string) =>
    UrlParamsReplace('/brands/:id', { id });
export const GET_BRAND_BY_SLUG = (slug: string) =>
    UrlParamsReplace('/brands/slug/:slug', { slug });
// clinicAlerts

export const GET_CLINIC_ALERTS = (clinicId: string) =>
    UrlParamsReplace('/clinic-alerts/:clinicId', { clinicId }, {});

export const CREATE_CLINIC_ALERTS = UrlParamsReplace('/clinic-alerts', {}, {});

export const DELETE_CLINIC_ALERTS = (id: string) =>
    UrlParamsReplace('/clinic-alerts/:id', { id }, {});

export const UPDATE_CLINIC_ALERTS = (id: string) =>
    UrlParamsReplace('/clinic-alerts/:id', { id }, {});
// Delete from S3
export const DELETE_FROM_S3 = (fileKey: string) => {
    return UrlParamsReplace('/aws-s3', {}, { fileKey });
};

//patient-vaccinations

export const CREATE_PATIENT_VACCINATION = UrlParamsReplace(
    '/patient-vaccinations',
    {},
    {}
);
export const GET_PATIENT_VACCINATION = (patientId: string) =>
    UrlParamsReplace('/patient-vaccinations/:patientId', { patientId }, {});
export const UPDATE_PATIENT_VACCINATION = (id: string) =>
    UrlParamsReplace('/patient-vaccinations/:id', { id }, {});
// Add to cart
export const ADD_TO_CART = () => UrlParamsReplace('/cart-items');

// Delete from cart-item
export const DELETE_FROM_CART = (id: string, source?: string) => {
    const baseUrl = UrlParamsReplace('/cart-items/:id', { id });
    return source ? `${baseUrl}?source=${source}` : baseUrl;
};

// Get cart list for an appointment
export const GET_CART_LIST_FOR_AN_APPOINTMENT = (appointmentId: string) => {
    return UrlParamsReplace(
        '/cart-items',
        {},
        {
            appointmentId,
        }
    );
};
export const GET_CART_LIST_BY_CART_ID = (cartId: string) => {
    return UrlParamsReplace(
        '/cart-items',
        {},
        {
            cartId,
        }
    );
};
// Delete entire cart
export const DELETE_CART = (id: string) => {
    return UrlParamsReplace('/carts/:id', { id });
};
// Update cart details
export const UPDATE_CART_DETAILS = (cartId: string, source?: string) => {
    const baseUrl = UrlParamsReplace('/cart-items/:id/details', { id: cartId });
    return source ? `${baseUrl}?source=${source}` : baseUrl;
};

// Create invoice
export const CREATE_INVOICE = () => {
    return UrlParamsReplace('/invoices');
};

// Update invoice
export const UPDATE_INVOICE = (invoiceId: string) => {
    return UrlParamsReplace('/invoices/:invoiceId', { invoiceId });
};

// Delete invoice
export const DELETE_INVOICE = (invoiceId: string) => {
    return UrlParamsReplace('/invoices/:invoiceId', { invoiceId });
};

// Write off invoice
export const WRITE_OFF_INVOICE = (invoiceId: string) => {
    return UrlParamsReplace('/invoices/:invoiceId/write-off', { invoiceId });
};

// Create invoice with payment and appointment completion (atomic operation)
export const CREATE_INVOICE_WITH_PAYMENT = () => {
    return UrlParamsReplace('/invoices/create-invoice-with-payment');
};

// Create payment details - Credit collect, Credit return, Invoice
export const CREATE_PAYMENT_DETAILS = () => {
    return UrlParamsReplace('/payment-details');
};

// Create bulk payment for multiple invoices
export const CREATE_BULK_PAYMENT_DETAILS = () => {
    return UrlParamsReplace('/payment-details/bulk');
};

export const UPLOAD_CLINIC_EXCEL = (clinicId: string, brandId: string) =>
    UrlParamsReplace(
        `/clinics/bulk-upload?clinicId=${clinicId}&brandId=${brandId}`
    );

// Get Endpoints
export const GET_CLINIC_CONSUMABLES = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-consumables', {}, { clinicId, page, limit });

export const GET_CLINIC_PRODUCTS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-products', {}, { clinicId, page, limit });

export const GET_CLINIC_MEDICATIONS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-medications', {}, { clinicId, page, limit });

export const GET_CLINIC_VACCINATIONS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-vaccinations', {}, { clinicId, page, limit });

export const GET_CLINIC_SERVICES = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-services', {}, { clinicId, page, limit });

export const GET_CLINIC_DIAGNOSTICS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) =>
    UrlParamsReplace(
        '/clinic-lab-reports/types',
        {},
        { clinicId, page, limit, integrationType: '' }
    );

// create Endpoints
export const CREATE_CLINIC_CONSUMABLES = () =>
    UrlParamsReplace('/clinic-consumables');

export const CREATE_CLINIC_PRODUCTS = () =>
    UrlParamsReplace('/clinic-products');

export const CREATE_CLINIC_MEDICATIONS = () =>
    UrlParamsReplace('/clinic-medications');

export const CREATE_CLINIC_VACCINATIONS = () =>
    UrlParamsReplace('/clinic-vaccinations');

export const CREATE_CLINIC_SERVICES = () =>
    UrlParamsReplace('/clinic-services');

export const CREATE_CLINIC_DIAGNOSTICS = () =>
    UrlParamsReplace('/clinic-lab-reports');

// Update Endpoints
export const UPDATE_CLINIC_CONSUMABLES = (id: string) =>
    UrlParamsReplace('/clinic-consumables/:id', { id });

export const UPDATE_CLINIC_PRODUCTS = (id: string) =>
    UrlParamsReplace('/clinic-products/:id', { id });

export const UPDATE_CLINIC_MEDICATIONS = (id: string) =>
    UrlParamsReplace('/clinic-medications/:id', { id });

export const UPDATE_CLINIC_VACCINATIONS = (id: string) =>
    UrlParamsReplace('/clinic-vaccinations/:id', { id });

export const UPDATE_CLINIC_SERVICES = (id: string) =>
    UrlParamsReplace('/clinic-services/:id', { id });

export const UPDATE_CLINIC_DIAGNOSTICS = (id: string) =>
    UrlParamsReplace('/clinic-lab-reports/:id', { id });

// Delete Endpoints
export const DELETE_CLINIC_CONSUMABLES = (id: string) =>
    UrlParamsReplace('/clinic-consumables/:id', { id });

export const DELETE_CLINIC_PRODUCTS = (id: string) =>
    UrlParamsReplace('/clinic-products/:id', { id });

export const DELETE_CLINIC_MEDICATIONS = (id: string) =>
    UrlParamsReplace('/clinic-medications/:id', { id });

export const DELETE_CLINIC_VACCINATIONS = (id: string) =>
    UrlParamsReplace('/clinic-vaccinations/:id', { id });

export const DELETE_CLINIC_SERVICES = (id: string) =>
    UrlParamsReplace('/clinic-services/:id', { id });

export const DELETE_CLINIC_DIAGNOSTICS = (id: string) =>
    UrlParamsReplace('/clinic-lab-reports/:id', { id });

export const DOWNLOAD_LATEST_INVENTORY = (clinicId: string) =>
    UrlParamsReplace('/clinics/inventory/download/:clinicId', { clinicId });

export const DELETE_INVENTORY_ITEM = (itemType: string, itemId: string) =>
    UrlParamsReplace(`/clinics/inventory/${itemType}/:itemId`, { itemId });
export const CREATE_CLINIC = () => UrlParamsReplace('/clinics');

export const GET_ALL_CLINICS = (
    page?: number,
    limit?: number,
    orderBy?: string
) => {
    const params: any = {};
    if (page !== undefined) params.page = page;
    if (limit !== undefined) params.limit = limit;
    if (orderBy !== undefined) params.orderBy = orderBy;

    return UrlParamsReplace('/clinics', {}, params);
};

export const GET_CLINIC = (id: string) => {
    return UrlParamsReplace('/clinics/:id', { id });
};
export const DEACTIVATE_CLINIC = (id: string) =>
    UrlParamsReplace('/clinics/:id/deactivate', { id });

export const REACTIVATE_CLINIC = (id: string) =>
    UrlParamsReplace('/clinics/:id/reactivate', { id });

export const SOFT_DELETE_CLINIC = (id: string) =>
    UrlParamsReplace('/clinics/:id', { id });

export const EDIT_CLINIC_DETAILS = (id: string) =>
    UrlParamsReplace('/clinics/basic/:id', { id });

// Get payment details list for a patient id
export const GET_PAYMENT_DETAILS_LIST_FOR_A_PATIENT = (patientId: string) => {
    return UrlParamsReplace('/payment-details/:patientId', { patientId });
};

// Get payment details list for a owner id
export const GET_PAYMENT_DETAILS_LIST_FOR_A_OWNER = (ownerId: string) => {
    return UrlParamsReplace('/payment-details/owner-receipts/:ownerId', {
        ownerId,
    });
};

// Get payment receipts list for a patient id
export const GET_PAYMENT_RECEIPTS_LIST_FOR_A_PATIENT = (patientId: string) => {
    return UrlParamsReplace('/payment-details/patient-receipts/:patientId', {
        patientId,
    });
};

// Get invoices list for a patient id
export const GET_INVOICES_LIST_FOR_A_PATIENT = (patientId: string) => {
    return UrlParamsReplace('/payment-details/patient-invoices/:patientId', {
        patientId,
    });
};

export const GET_CLINIC_USER_DATA = (userId: string) =>
    UrlParamsReplace('/users/clinic-user/:userId', { userId });

export const GET_ALL_CLINIC_DOCTORS = (clinicId: string) =>
    UrlParamsReplace('/users/clinic/doctors/:clinicId', { clinicId });

export const UPDATE_WORKING_HOURS = (userId: string) =>
    UrlParamsReplace('/users/working-hours/:userId', { userId }); // userId here is

export const GET_USER_CLINICS = (userId: string) =>
    UrlParamsReplace('/users/clinics/:userId', { userId });

export const GET_USER_EXCEPTIONS = (
    clinicUserId: string,
    includeHistory: boolean = false
) =>
    UrlParamsReplace(
        '/users/exceptions/:clinicUserId',
        { clinicUserId },
        { includeHistory: String(includeHistory) }
    );

export const GET_CALENDAR_WORKING_HOURS = (date: string, clinicId: string) =>
    UrlParamsReplace('/users/calendar-working-hours', {}, { date, clinicId });

export const GET_EXCEPTION_DETAILS = (id: string) =>
    UrlParamsReplace('/users/exceptions/detail/:id', { id });

export const CREATE_EXCEPTION = () => UrlParamsReplace('/users/exceptions');

export const UPDATE_EXCEPTION = (id: string) =>
    UrlParamsReplace('/users/exceptions/:id', { id });

export const DELETE_EXCEPTION = (id: string) =>
    UrlParamsReplace('/users/exceptions/:id', { id });

export const ADD_LONG_TERM_PRESCRIPTION_TO_CART = () =>
    UrlParamsReplace('/cart-items/bulkInsert');

export const DELETE_DIAGNOSTICS = (appointmentId: string) => {
    return UrlParamsReplace('/clinic-lab-reports/appointment/:appointmentId', {
        appointmentId,
    });
};

export const GET_CLINIC_DETAILS = (clinicId: string) => {
    return UrlParamsReplace('/clinics/:clinicId', { clinicId });
};

/*********************** IDEXX **********************/
// Create Idexx entry
export const CREATE_IDEXX_ENTRY = () => {
    return UrlParamsReplace('/clinic-integrations');
};

// Get idexx entries
export const GET_IDEXX_ENTRIES = (clinicId: string) => {
    return UrlParamsReplace('/clinic-integrations', {}, { clinicId });
};

// Delete idexx entry
export const DELETE_IDEXX_ENTRY = (clinicIdexxId: string) => {
    return UrlParamsReplace('/clinic-integrations/:clinicIdexxId', {
        clinicIdexxId,
    });
};

// Get IDEXX test list
export const GET_IDEXX_TESTS_LIST = (clinicId: string) => {
    return UrlParamsReplace('/clinic-integrations/:clinicId/testsList', {
        clinicId,
    });
};

// Add IDEXX test item into clinic_lab_reports table
export const ADD_IDEXX_TEST_ITEM_TO_LABREPORTS_LIST = (clinicId: string) => {
    return UrlParamsReplace('/clinic-integrations/:clinicId/create', {
        clinicId,
    });
};

// Get all the selected/added IDEXX test lists
export const GET_SELECTED_IDEXX_TESTS_LIST = (
    search: string,
    clinicId: string,
    integrationType: string
) =>
    UrlParamsReplace(
        '/clinic-lab-reports/types',
        { search },
        { clinicId, integrationType }
    );

// Delete idexx test list item
export const DELETE_IDEXX_TEST_LIST_ITEM = (clinicIdexxTestItemId: string) => {
    return UrlParamsReplace(
        '/clinic-integrations/labReport/:clinicIdexxTestItemId',
        {
            clinicIdexxTestItemId,
        }
    );
};

// Create an IDEXX order
export const CREATE_IDEXX_ORDER = () =>
    UrlParamsReplace('/clinic-integrations/create');

// Cancel an IDEXX order
export const CANCEL_IDEXX_ORDER = (clinicId: string, idexxOrderId: string) =>
    UrlParamsReplace(
        '/clinic-integrations/clinic/:clinicId/cancel/:idexxOrderId',
        { clinicId, idexxOrderId }
    );

// Check if IDEXX orders can be deleted by verifying their status
export const CHECK_IDEXX_ORDERS_DELETION_ELIGIBILITY = (clinicId: string) =>
    UrlParamsReplace(
        '/clinic-integrations/clinic/:clinicId/check-deletion-eligibility',
        { clinicId }
    );

export const UPDATE_CLINIC_LAB_REPORT = (id: string) => {
    return UrlParamsReplace('/clinic-lab-reports/:id', { id });
};

export const CREATE_EMR = UrlParamsReplace('/emr');
export const SEND_INDIVIDUAL_EMR = (
    appointmentId: string,
    shareMode: string,
    documentType: string,
    params?: { fileKeys?: string }
) =>
    UrlParamsReplace(
        `/emr/documents/:appointmentId`,
        { appointmentId },
        {
            shareMode,
            documentType,
            ...(params?.fileKeys ? { fileKeys: params.fileKeys } : {}),
        }
    );

export const SEND_VACCINATION_DOCUMENTS = (
    fileKey: string,
    shareMode: string,
    appointmentId: string,
    clinicId: string,
    brandId: string,
    patientId: string,
    email?: string,
    phoneNumber?: string,
    recipientType?: 'client' | 'other'
) => {
    // Use Record<string, any> to allow any properties
    let queryParams: Record<string, any> = {
        fileKey,
        shareMode,
        appointmentId,
        clinicId,
        brandId,
        patientId,
    };

    // Add custom recipient parameters if provided
    if (recipientType === 'other') {
        queryParams = {
            ...queryParams,
            recipientType,
            ...(email ? { email } : {}),
            ...(phoneNumber ? { phoneNumber } : {}),
        };
    }

    return UrlParamsReplace(`/emr/vaccinations`, {}, queryParams);
};

export const SEND_INVOICE_TAB_DOCUMENT = (
    patientId: string,
    fileKeys: string[],
    shareMode: string
) =>
    UrlParamsReplace(
        `/emr/documents/invoices/:patientId`,
        { patientId },
        { fileKeys: JSON.stringify(fileKeys), shareMode }
    );

export const SEND_MEDICAL_RECORD_DOCUMENTS = (
    patientId: string,
    shareMode: string,
    documentType: string
) =>
    UrlParamsReplace(
        `/emr/patient/medical-records/:patientId`,
        { patientId },
        { shareMode, documentType }
    );

export const DOCUMENT_AVAILABLE_FOR_APPOINTMENT = (appointmentId: string) =>
    UrlParamsReplace(
        `/emr/patient/document-availabe/:appointmentId`,
        { appointmentId },
        {}
    );

export const DOCUMENT_AVAILABLE_FOR_PATIENT = (patientId: string) =>
    UrlParamsReplace(
        `/emr/patient/document-availabe-patient/:patientId`,
        { patientId },
        {}
    );

export const FIND_SINGLE_EMR = (appointmentId: string) =>
    UrlParamsReplace(`/emr/individual/:appointmentId`, { appointmentId });

// AI Integration
export const GENERATE_SOAP_NOTES = () => UrlParamsReplace('/ai/soap');

export const DOWNLOAD_TODAYS_APPOINTMENT = (date: string, clinicId: string) =>
    UrlParamsReplace(
        '/appointments/clinic/today-Appointment',
        {},
        { date, clinicId }
    );

// Patients-Reminder
export const CREATE_PATIENT_REMINDER = (patientId: string) =>
    UrlParamsReplace('/patients-reminders/:patientId', { patientId });

export const GET_PATIENT_REMINDERS = (
    patientId: string,
    page: number,
    limit: number
) =>
    UrlParamsReplace(
        '/patients-reminders/:patientId',
        { patientId },
        { page, limit }
    );

export const DELETE_PATIENT_REMINDER = (
    patientId: string,
    reminderId: string
) =>
    UrlParamsReplace('/patients-reminders/:patientId/:id', {
        patientId,
        id: reminderId,
    });

export const COMPLETE_PATIENT_REMINDER = (
    patientId: string,
    reminderId: string
) =>
    UrlParamsReplace('/patients-reminders/:patientId/:id/complete', {
        patientId,
        id: reminderId,
    });

export const OVERRIDDEN_PATIENT_REMINDER = (
    patientId: string,
    reminderId: string
) =>
    UrlParamsReplace('/patients-reminders/:patientId/:id/overridden', {
        patientId,
        id: reminderId,
    });

export const UPDATE_REMINDER = (patientId: string, reminderId: string) =>
    UrlParamsReplace('/patients-reminders/:patientId/:reminderId', {
        patientId,
        reminderId,
    });

export const MARK_REMINDER_INCOMPLETE = (
    patientId: string,
    reminderId: string
) =>
    UrlParamsReplace('/patients-reminders/:patientId/:id/incomplete', {
        patientId,
        id: reminderId,
    });

//document library

export const CREATE_DOCUMENT_LIBRARY = UrlParamsReplace('/document-library');

export const GET_DOCUMENT_LIBRARY = (
    clinicId: string,
    page: number = 1,
    limit: number = 10,
    search: string = ''
) =>
    UrlParamsReplace(
        '/document-library',
        {},
        { page, limit, search, clinicId }
    );

export const DELETE_DOCUMENT_LIBRARY = (id: string) =>
    UrlParamsReplace('/document-library/:id', { id });

export const UPDATE_DOCUMENT_LIBRARY = (id: string) =>
    UrlParamsReplace('/document-library/:id', { id });

export const SEND_DOCUMENT_LIBRARY_TO_PATIENT_OWNER = UrlParamsReplace(
    '/patient-document-libraries'
);

export const GET_DOCUMENT_LIBRARY_TO_PATIENT_OWNER = (
    page: number = 1,
    limit: number = 10,
    search: string = '',
    patientId: string
) =>
    UrlParamsReplace(
        '/patient-document-libraries/:patientId',
        { patientId },
        { page, limit, search }
    );

export const GET_SINGLE_PATIENT_DOCUMENT_LIBRARY = (id: string) =>
    UrlParamsReplace(`/patient-document-libraries/document/:id`, { id }, {});

export const SEND_SINGED_PATIENT_DOCUMENT_LIBRARY = (id: string) =>
    UrlParamsReplace(`/patient-document-libraries/:id`, { id }, {});

export const CREATE_DIAGNOSTIC_NOTES = (
    id: string,
    operation: 'insert' | 'edit' | 'delete'
) =>
    UrlParamsReplace(
        `/clinic-lab-reports/:id/diagnostic-notes/create`,
        { id },
        { operation }
    );
export const DOWNLOAD_ANALYTICS_REPORT = (dto: {
    type: string;
    startDate: string;
    endDate: string;
    clinicId: string;
    reportType?: string;
}) =>
    UrlParamsReplace(
        '/analytics/download-report',
        {},
        {
            type: dto.type,
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
            ...(dto.reportType ? { reportType: dto.reportType } : {}),
        }
    );

export const GET_REVENUE_CHART_DATA = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
}) =>
    UrlParamsReplace(
        '/analytics/revenue-chart-data',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
        }
    );

export const CREATE_GLOBAL_REMINDER = () =>
    UrlParamsReplace('/global-reminders');

export const GET_GLOBAL_REMINDERS = (
    clinicId: string,
    page: number,
    limit: number,
    search: string
) =>
    UrlParamsReplace(
        '/global-reminders/clinic/:clinicId',
        { clinicId },
        { page, limit, search }
    );

export const UPDATE_GLOBAL_REMINDER = (reminderId: string) =>
    UrlParamsReplace('/global-reminders/:id', { id: reminderId });

export const DELETE_GLOBAL_REMINDER = (reminderId: string) =>
    UrlParamsReplace('/global-reminders/:id', { id: reminderId });

// Diagnostic Notes
export const CREATE_DIAGNOSTIC_TEMPLATE = () =>
    UrlParamsReplace('/diagnostic-templates');

export const GET_DIAGNOSTIC_TEMPLATES = (clinicId: string) =>
    UrlParamsReplace('/diagnostic-templates', {}, { clinicId });

export const UPDATE_DIAGNOSTIC_TEMPLATE = (
    templateId: string,
    clinicId: string
) => UrlParamsReplace(`/diagnostic-templates/${templateId}`, {}, { clinicId });

export const DELETE_DIAGNOSTIC_TEMPLATE = (
    templateId: string,
    clinicId: string
) => UrlParamsReplace(`/diagnostic-templates/${templateId}`, {}, { clinicId });

// Diagnostic Note
export const CREATE_DIAGNOSTIC_NOTE = () =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note');

export const GET_DIAGNOSTIC_NOTES = (labReportId: string, clinicId: string) =>
    UrlParamsReplace(
        '/diagnostic-templates/lab-report/:labReportId',
        { labReportId },
        { clinicId }
    );

export const GET_DIAGNOSTIC_NOTE = (noteId: string) =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note/note/:noteId', {
        noteId,
    });

export const UPDATE_DIAGNOSTIC_NOTE = (noteId: string) =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note/:noteId', {
        noteId,
    });

export const DELETE_DIAGNOSTIC_NOTE = (noteId: string) =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note/:noteId', {
        noteId,
    });

export const GET_DIAGNOSTIC_NOTES_FOR_PATIENT = (patientId: string) =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note/:patientId', {
        patientId,
    });

// Templates for specific lab report
export const GET_LAB_REPORT_TEMPLATES = (
    clinicLabReportId: string,
    clinicId: string
) =>
    UrlParamsReplace(
        '/diagnostic-templates/clinic-lab-report/:clinicLabReportId',
        { clinicLabReportId },
        { clinicId }
    );
export const GET_OWNER_PATIENTS = (id: string) =>
    UrlParamsReplace('/owners/:id/patients', { id });

export const GET_CLINIC_OWNERS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10,
    search: string = ''
) => {
    const params: any = { page, limit };
    if (search) {
        params.search = search;
    }
    return UrlParamsReplace('/owners/clinic/:clinicId', { clinicId }, params);
};

export const TRANSFER_OWNERSHIP = () => UrlParamsReplace('/owners/transfer');

export const UPDATE_OWNER = (id: string) =>
    UrlParamsReplace('/owners/:id', { id });

export const CREATE_OWNER = () => UrlParamsReplace('/owners');

export const REMOVE_OWNER_FROM_PATIENT = (ownerId: string, patientId: string) =>
    UrlParamsReplace('/owners/:ownerId/patient/:patientId', {
        ownerId,
        patientId,
    });
export const CREATE_PATIENT_TREATMENT_ESTIMATE =
    UrlParamsReplace('/patient-estimates');

export const GET_PATIENT_TREATMENT_ESTIMATE = (id: string) =>
    UrlParamsReplace('/patient-estimates/:id', { id });

export const GET_TREATMENT_ESTIMATE_FOR_PATIENT = (
    page: number = 1,
    limit: number = 10,
    search: string = '',
    patientId: string
) =>
    UrlParamsReplace(
        '/patient-estimates/patient/:patientId',
        { patientId },
        { page, limit, search }
    );

export const SEND_SIGNED_TREATMENT_ESTIMATE = (id: string) =>
    UrlParamsReplace('/patient-estimates/:id', { id });

export const SHARE_EMR_DOCUMENT = (
    appointmentId: string,
    shareMode: string,
    type: 'client' | 'other',
    email: string,
    phoneNumber: string
) =>
    UrlParamsReplace(
        `/emr/documents/emr/:appointmentId`,
        { appointmentId },
        { shareMode, type, email, phoneNumber }
    );
export const GET_COLLECTED_PAYMENTS_CHART_DATA = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
}) =>
    UrlParamsReplace(
        '/analytics/collected-payments-chart-data',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
        }
    );

export const GET_APPOINTMENTS_CHART_DATA = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
    type: string;
}) =>
    UrlParamsReplace(
        '/analytics/appointments-chart-data',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
            type: dto.type,
        }
    );

export const SHARE_PRESCRIPTION_DOCUMENT = (
    appointmentId: string,
    shareMode: string,
    type: 'client' | 'other',
    email: string,
    phoneNumber: string
) =>
    UrlParamsReplace(
        `/emr/documents/prescription/:appointmentId`,
        { appointmentId },
        { shareMode, type, email, phoneNumber }
    );

export const SHARE_SUPPORTING_DOCUMENT = (
    appointmentId: string,
    shareMode: string,
    type: 'client' | 'other',
    email: string,
    phoneNumber: string
) =>
    UrlParamsReplace(
        `/emr/documents/supporting-documents/:appointmentId`,
        { appointmentId },
        { shareMode, type, email, phoneNumber }
    );

export const CREATE_PRESCRIPTION_DOCUMENT = UrlParamsReplace(
    `/emr/create-prescription`
);

export const FIND_PRESCRIPTION_FILEKEY = (appointmentId: string) =>
    UrlParamsReplace(`/emr/prescription/:appointmentId`, { appointmentId });
export const GET_DOCTOR_SUMMARY = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
}) =>
    UrlParamsReplace(
        '/analytics/doctor-summary',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
        }
    );

export const GET_SUMMARY = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
}) =>
    UrlParamsReplace(
        '/analytics/summary',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
        }
    );

export const SHARE_LEDGER_DOCUMENT = (
    ownerId: string,
    shareMode: string,
    type: 'client' | 'other',
    email: string,
    phoneNumber: string,
    filters?: {
        userId?: string;
        status?: string;
        startDate?: string;
        endDate?: string;
        searchTerm?: string;
        paymentMode?: string;
    }
) => {
    const queryParams: Record<string, string> = {
        shareMode,
        type,
        email,
        phoneNumber,
        ownerId,
    };

    // Add filter parameters if provided
    if (filters?.userId) queryParams.userId = filters.userId;
    if (filters?.status) queryParams.status = filters.status;
    if (filters?.startDate) queryParams.startDate = filters.startDate;
    if (filters?.endDate) queryParams.endDate = filters.endDate;
    if (filters?.searchTerm) queryParams.searchTerm = filters.searchTerm;
    if (filters?.paymentMode) queryParams.paymentMode = filters.paymentMode;

    return UrlParamsReplace(`/emr/ledger`, {}, queryParams);
};

export const DOWNLOAD_LEDGER_DOCUMENT = (
    ownerId: string,
    filters?: {
        userId?: string;
        status?: string;
        startDate?: string;
        endDate?: string;
        searchTerm?: string;
        paymentMode?: string;
    }
) => {
    const queryParams: Record<string, string> = { ownerId };

    // Add filter parameters if provided
    if (filters?.userId) queryParams.userId = filters.userId;
    if (filters?.status) queryParams.status = filters.status;
    if (filters?.startDate) queryParams.startDate = filters.startDate;
    if (filters?.endDate) queryParams.endDate = filters.endDate;
    if (filters?.searchTerm) queryParams.searchTerm = filters.searchTerm;
    if (filters?.paymentMode) queryParams.paymentMode = filters.paymentMode;

    return UrlParamsReplace(`/emr/store/ledger`, {}, queryParams);
};

export const LEDGER_DOCUMENT_STATUS_URL = (requestId: string) =>
    UrlParamsReplace(`/emr/ledger/status/:requestId`, { requestId }, {});

export const GET_INDIVIDUAL_PAYEMENT_DETAIL = (id: string) =>
    UrlParamsReplace(`/payment-details/findOne/:id`, { id }, {});

export const DELETE_LEDGER_DOCUMENT_FILEKEY = (id: string) =>
    UrlParamsReplace('/payment-details/delete-ledger/:id', { id }, {});

export const GET_LAST_ACTIVITY = (tabName: string, reference_id: string) =>
    UrlParamsReplace('/tab-activities/last/:tabName/:referenceId', {
        tabName,
        referenceId: reference_id,
    });

export const CREATE_TAB_ACTIVITY = () =>
    UrlParamsReplace('/tab-activities', {});

// Get owner invoices with payments (with filters)
export const GET_OWNER_INVOICES_WITH_PAYMENTS = (
    ownerId: string,
    page: number = 1,
    limit: number = 10,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        status?: string;
        paymentMode?: string;
        searchTerm?: string;
        userId?: string;
        invoiceType?: string;
    }
) => {
    const queryParams: Record<string, string> = {
        page: page.toString(),
        limit: limit.toString(),
    };

    if (filters?.startDate) queryParams.startDate = filters.startDate;
    if (filters?.endDate) queryParams.endDate = filters.endDate;
    if (filters?.petName) queryParams.petName = filters.petName;
    if (filters?.status) queryParams.status = filters.status;
    if (filters?.paymentMode) queryParams.paymentMode = filters.paymentMode;
    if (filters?.searchTerm) queryParams.searchTerm = filters.searchTerm;
    if (filters?.userId) queryParams.userId = filters.userId;
    if (filters?.invoiceType) queryParams.invoiceType = filters.invoiceType;

    return UrlParamsReplace(
        '/payment-details/owner-invoices/:ownerId',
        { ownerId },
        queryParams
    );
};

// Get pending invoices for an owner (with filtering options)
export const GET_OWNER_PENDING_INVOICES = (
    ownerId: string,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        searchTerm?: string;
    }
) => {
    return UrlParamsReplace(
        '/payment-details/pending-invoices/:ownerId',
        { ownerId },
        filters
    );
};

// Get owner ledger (combined invoices and payments chronologically)
export const GET_OWNER_LEDGER = (ownerId: string) => {
    return UrlParamsReplace('/payment-details/owner-ledger/:ownerId', {
        ownerId,
    });
};

// Update document URL helpers to match existing API endpoints
export const INVOICE_DOCUMENT_URL = (
    referenceAlphaId: string,
    action: 'download' | 'share',
    patientId: string,
    shareMethod?: 'email' | 'whatsapp' | 'both',
    recipient?: 'client' | 'other',
    email?: string,
    whatsapp?: string
) => {
    const queryParams: Record<string, string> = {
        action,
        patientId,
    };

    if (action === 'share' && shareMethod) {
        queryParams.shareMethod = shareMethod;

        // Add recipient type if provided
        if (recipient) {
            queryParams.recipient = recipient;
        }

        // Add email and whatsapp values if provided
        if (email) {
            queryParams.email = email;
        }

        if (whatsapp) {
            queryParams.phoneNumber = whatsapp;
        }
    }

    return UrlParamsReplace(
        '/invoices/documents/:referenceAlphaId',
        { referenceAlphaId },
        queryParams
    );
};

// New endpoint for checking invoice document status (polling)
export const INVOICE_DOCUMENT_STATUS_URL = (invoiceId: string) => {
    return UrlParamsReplace('/invoices/document-status/:invoiceId', {
        invoiceId,
    });
};

export const PAYMENT_DOCUMENT_STATUS_URL = (referenceAlphaId: string) => {
    return UrlParamsReplace(
        `/payment-details/document-status/:referenceAlphaId`,
        { referenceAlphaId }
    );
};

export const PAYMENT_DOCUMENT_URL = (
    referenceAlphaId: string,
    documentType: 'creditnote' | 'payment-details',
    action: 'download' | 'share',
    shareMethod?: 'email' | 'whatsapp' | 'both',
    recipient?: 'client' | 'other',
    email?: string,
    whatsapp?: string
) => {
    const queryParams: Record<string, string> = {
        documentType,
        action,
    };

    if (action === 'share' && shareMethod) {
        queryParams.shareMethod = shareMethod;

        // Add recipient type if provided
        if (recipient) {
            queryParams.recipient = recipient;
        }

        // Add email and whatsapp values if provided
        if (email) {
            queryParams.email = email;
        }

        if (whatsapp) {
            queryParams.phoneNumber = whatsapp;
        }
    }

    return UrlParamsReplace(
        '/payment-details/documents/:referenceAlphaId',
        { referenceAlphaId },
        queryParams
    );
};

// Client Booking Settings
export const GET_CLIENT_BOOKING_SETTINGS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/client-booking-settings', {
        clinicId,
    });

export const UPDATE_CLIENT_BOOKING_SETTINGS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/client-booking-settings', {
        clinicId,
    });
// End Client Booking Settings

// Clinic Settings
export const GET_CLINIC_SETTINGS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/settings', {
        clinicId,
    });

export const UPDATE_CLINIC_SETTINGS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/settings', {
        clinicId,
    });
// End Clinic Settings
// --- Payment Receipts URLs --- Start ---
export const SHARE_PAYMENT_RECEIPTS_URL = (
    patientId: string,
    shareMode: string, // JSON string array, e.g., '["email","whatsapp"]'
    type: 'client' | 'other',
    email: string, // Required if type is 'other' and email is in shareMode
    phoneNumber: string // Required if type is 'other' and whatsapp is in shareMode
) =>
    UrlParamsReplace(
        `/emr/receipts/share`, // Matches the backend controller path
        {},
        { patientId, shareMode, type, email, phoneNumber }
    );

export const STORE_PAYMENT_RECEIPTS_URL = (patientId: string) =>
    UrlParamsReplace(`/emr/receipts/store`, {}, { patientId }); // Matches the backend controller path

export const PAYMENT_RECEIPTS_STATUS_URL = (requestId: string) =>
    UrlParamsReplace(`/emr/receipts/status/:requestId`, { requestId }, {}); // Matches the backend controller path
// --- Payment Receipts URLs --- End ---

// --- Statement URLs --- Start ---
export const REQUEST_STATEMENT_DOCUMENTS = (
    ownerId: string,
    types: string[],
    action: 'DOWNLOAD' | 'SHARE',
    shareMethod?: 'email' | 'whatsapp' | 'both',
    recipient?: 'client' | 'other',
    email?: string,
    phoneNumber?: string,
    filters?: {
        userId?: string;
        status?: string;
        startDate?: string;
        endDate?: string;
        searchTerm?: string;
        paymentMode?: string;
    }
) => {
    const queryParams: Record<string, string> = {
        types: types.join(','),
        action,
    };

    // Add share-specific parameters if action is SHARE
    if (action === 'SHARE' && shareMethod) {
        queryParams.shareMethod = shareMethod;

        if (recipient) {
            queryParams.recipient = recipient;

            // Add email and phoneNumber if recipient is 'other'
            if (recipient === 'other') {
                if (email) queryParams.email = email;
                if (phoneNumber) queryParams.phoneNumber = phoneNumber;
            }
        }
    }

    // Add filter parameters if provided
    if (filters) {
        if (filters.userId) queryParams.userId = filters.userId;
        if (filters.status) queryParams.status = filters.status;
        if (filters.startDate) queryParams.startDate = filters.startDate;
        if (filters.endDate) queryParams.endDate = filters.endDate;
        if (filters.searchTerm) queryParams.searchTerm = filters.searchTerm;
        if (filters.paymentMode) queryParams.paymentMode = filters.paymentMode;
    }

    return UrlParamsReplace(
        `/statements/owner/:ownerId/documents`,
        { ownerId },
        queryParams
    );
};

export const STATEMENT_DOCUMENT_STATUS_URL = (requestId: string) =>
    UrlParamsReplace(
        `/statements/documents/status/:requestId`,
        { requestId },
        {}
    );
// --- Statement URLs --- End ---

// --- Credits URLs --- Start ---
export const GET_OWNER_CREDIT_TRANSACTIONS = (
    ownerId: string,
    filters?: {
        page?: number;
        limit?: number;
        searchTerm?: string;
        transactionType?: string;
        derivedTransactionTypes?: string;
        startDate?: string;
        endDate?: string;
        userId?: string;
    }
) => {
    const queryParams: Record<string, string> = {};

    if (filters?.page) queryParams.page = filters.page.toString();
    if (filters?.limit) queryParams.limit = filters.limit.toString();
    if (filters?.searchTerm) queryParams.searchTerm = filters.searchTerm;
    if (filters?.transactionType)
        queryParams.transactionType = filters.transactionType;
    if (filters?.derivedTransactionTypes)
        queryParams.derivedTransactionTypes = filters.derivedTransactionTypes;
    if (filters?.startDate) queryParams.startDate = filters.startDate;
    if (filters?.endDate) queryParams.endDate = filters.endDate;
    if (filters?.userId) queryParams.userId = filters.userId;

    return UrlParamsReplace(
        '/credits/owner/:ownerId/transactions-paginated',
        { ownerId },
        queryParams
    );
};
// --- Credits URLs --- End ---

// Edit payment details
export const EDIT_PAYMENT_DETAILS = (paymentId: string) => {
    return UrlParamsReplace('/payment-details/:id', { id: paymentId });
};

// Delete payment details
export const DELETE_PAYMENT_DETAILS = (paymentId: string) => {
    return UrlParamsReplace('/payment-details/:id', { id: paymentId });
};

```


### 📁 `ui/app/signin/pin/page.tsx`

**Lines:** 219 | **Size:** 8182 bytes

```typescript
'use client';

import React, { useEffect, useState } from 'react';
import { useLoginWithPinMutation } from '../../services/signin.queries';
import { useRouter } from 'next/navigation';
import PinSignInForm from '../../organisms/signin/PinSignIn';
import Alert from '../../atoms/Alert';
import Link from 'next/link';
import {
    getBrandId,
    setAuth,
    setBrandId,
} from '@/app/services/identity.service';
import { useSearchParams } from 'next/navigation';
import { getBrandBySlug } from '@/app/services/brands.services';

export default function SignUpPage() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const brandId = getBrandId();

    const [alert, setAlert] = useState<{
        variant: 'info' | 'error' | 'success' | 'warning';
        label: string;
        isOpen: boolean;
        payload?: any;
    }>({ variant: 'info', label: '', isOpen: false });
    const router = useRouter();
    const searchParams = useSearchParams();
    const status = searchParams.get('status');

    useEffect(() => {
        const detectBrandFromHostname = async () => {
            const fullHostname = window.location.hostname;

            const isProduction = fullHostname.includes('nidana.io');
            const isQA = fullHostname.includes('nidanaqa-api.napses.in');
            const isUAT = fullHostname.includes('nidana.tech');
            const isLocal = fullHostname.includes('localhost');

            let currentHost = '';
            let baseDomain = '';

            if (isProduction) {
                baseDomain = 'nidana.io';
            } else if (isQA) {
                baseDomain = 'nidanaqa-api.napses.in';
            } else if (isUAT) {
                baseDomain = 'nidana.tech';
            } else if (isLocal) {
                baseDomain = 'localhost:4201';
            }

            if (baseDomain) {
                currentHost = fullHostname.split('.')[0];
            }

            if (currentHost !== 'superadmin' && currentHost !== '') {
                try {
                    const brandInfo = await getBrandBySlug(
                        currentHost.split('.')[0]
                    );
                    const brandId = brandInfo?.data?.id;
                    const isSecure = window.location.protocol === 'https:';
                    if (brandId) {
                        setBrandId(brandId, isSecure);
                    }
                } catch (error) {
                    console.error('Error fetching brand info:', error);
                }
            }
        };

        detectBrandFromHostname();
    }, []);

    const { loginWithPinMutation } = useLoginWithPinMutation(brandId);

    const handleAlertClose = () => {
        setAlert({ ...alert, isOpen: false });
    };

    const onSubmit = (data: { pin: string }) => {
        setIsSubmitting(true);
        loginWithPinMutation.mutate(data.pin, {
            onSuccess: (response) => {
                if (response.status === false) {
                    // Check if it's an inactive clinic error
                    const errorMessage =
                        response?.rawError?.errors ||
                        response?.rawError?.message ||
                        response?.errorMessage ||
                        '';

                    if (
                        errorMessage.includes(
                            'This clinic is currently inactive'
                        ) ||
                        errorMessage.includes('clinic is currently inactive')
                    ) {
                        setAlert({
                            variant: 'error',
                            label: 'Clinic is deactivated. Please contact the admin.',
                            isOpen: false,
                            payload: 'inactiveClinic',
                        });
                    } else {
                        setAlert({
                            variant: 'error',
                            label: 'Invalid PIN entered. Please try again.',
                            isOpen: false,
                            payload: 'wrongPin',
                        });
                    }
                } else if (response.data) {
                    const {
                        globalUserId,
                        userId, // This is clinicUserId from clinic_users table
                        clinicId,
                        role,
                        token,
                        isFirstLogin,
                        isMultiClinic,
                        brandId,
                        username,
                        clinicName,
                        isFullyOnboarded,
                        brandName,
                        lastRoute,
                    } = response.data;

                    setAuth({
                        globalUserId,
                        userId,
                        clinicId,
                        token,
                        role,
                        isFirstLogin,
                        isMultiClinic,
                        brandId,
                        username,
                        clinicName,
                        brandName,
                    });
                    let redirectRoute = '/onboard'; // default
                    if (role === 'super_admin') {
                        // Super admin should go directly to dashboard, skip clinic selection
                        router.push('/dashboard');
                    } else if (role === 'admin') {
                        if (isFirstLogin || isMultiClinic) {
                            redirectRoute = '/onboard';
                        }
                        if (
                            lastRoute &&
                            lastRoute !== '/signin/pin' &&
                            lastRoute !== '/'
                        ) {
                            // Use last route if available and valid
                            redirectRoute = lastRoute;
                        } else {
                            redirectRoute = '/onboard';
                        }
                    } else {
                        if (
                            lastRoute &&
                            lastRoute !== '/signin/pin' &&
                            lastRoute !== '/'
                        ) {
                            // Use last route if available and valid
                            redirectRoute = lastRoute;
                        } else {
                            redirectRoute = '/onboard';
                        }
                    }
                    router.push(redirectRoute);
                } else {
                    setAlert({
                        variant: 'error',
                        label: 'Unexpected response format. Please try again.',
                        isOpen: true,
                    });
                }
            },
            onError: (error) => {
                console.error('Login error:', error);
                setAlert({
                    variant: 'error',
                    label: 'An error occurred during login. Please try again.',
                    isOpen: true,
                });
            },
            onSettled: () => {
                setIsSubmitting(false);
            },
        });
    };

    return (
        <div className="">
            <PinSignInForm
                onSubmit={onSubmit}
                isSubmitting={isSubmitting}
                alert={alert}
                status={status}
            />

            <div className="absolute z-50 top-5 w-[450px] left-1/2 -translate-x-1/2">
                {alert.isOpen && (
                    <Alert
                        variant={alert.variant}
                        label={alert.label}
                        onClose={handleAlertClose}
                        className="mb-4"
                    />
                )}
            </div>
        </div>
    );
}

```

---

*Report generated by Nidana Deploy Extension on 7/23/2025, 3:11:08 PM*
